package com.fxiaoke.document.convert.process;

import com.aspose.email.AlternateView;
import com.aspose.email.Attachment;
import com.aspose.email.LinkedResource;
import com.aspose.email.MailMessage;
import com.aspose.email.MsgLoadOptions;
import com.fxiaoke.document.convert.domain.constants.Constants;
import com.fxiaoke.document.convert.domain.preview.BaseArg;
import com.fxiaoke.document.convert.process.options.MsgOptions;
import com.fxiaoke.document.convert.utils.AES256Util;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Slf4j
@Component
public class MsgProcess {

  private final MsgOptions msgOptions;

  public MsgProcess(MsgOptions msgOptions) {
    this.msgOptions = msgOptions;
  }

  public final static String SEPARATOR = ":";

  /**
   * 将 MSG 文件内容转换为 HTML 字符串。
   *
   * @return 包含 MSG 文件内容的 HTML 字符串，如果发生错误则返回错误信息的 HTML。
   */
  public String toHtml(InputStream stream) {
    return msgOptions.toHtml(stream);
  }

  public String convertMsgToHtml(BaseArg arg) {
    int pageIndex = 1;
    URL resourceUrl = getClass().getClassLoader().getResource("mail_template.html");
    String templatePath = resourceUrl.getPath();// 实际的文件路径
    Path parent = Paths.get(arg.getFilePath()).getParent();
    String msgFilePath = arg.getFilePath();
    String outputHtmlFilePath = FilenameUtils.concat(parent.toString(),
        pageIndex + Constants.HTML_SUFFIX);
    String resourcePrefixCode = AES256Util.encode(
        arg.getEa() + SEPARATOR + arg.getPath() + SEPARATOR + pageIndex);
    String resourceRequestPrefix = Constants.RESOURCE_REQUEST_PREFIX + resourcePrefixCode + "/";
    String resourceDir = FilenameUtils.concat(parent.toString(), String.valueOf(pageIndex));
    new File(resourceDir).mkdirs();
    String imagesDir = resourceDir + "/";
    String attachmentDir = resourceDir + "/";
    try (FileInputStream inputStream = new FileInputStream(msgFilePath)) {
      // 配置加载选项
      MsgLoadOptions loadOptions = new MsgLoadOptions();
      loadOptions.setPreferredTextEncoding(StandardCharsets.UTF_8);
      // 加载邮件
      MailMessage mailMsg = MailMessage.load(inputStream, loadOptions);
      // 获取邮件基本信息
      String subject = mailMsg.getSubject();
      String from = mailMsg.getFrom().toString();
      String to = mailMsg.getTo().toString();
      String bodyHtml = mailMsg.getHtmlBody();
      java.util.Date sendDate = mailMsg.getDate();
      // 统一处理邮件内容（图片和附件）
      EmailProcessResult result = processCompleteEmailWithTemplate(mailMsg, bodyHtml,
          resourceRequestPrefix, imagesDir,
          attachmentDir);
      // 使用模板生成完整的HTML文档
      String completeHtml = generateEmailFromTemplate(templatePath, subject, from, to, sendDate,
          result.processedHtml, result.attachmentCount, result.totalAttachmentSize,
          result.attachmentHtml);
      // 保存HTML文件
      Files.writeString(Paths.get(outputHtmlFilePath), completeHtml);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return "";
    }
    return outputHtmlFilePath;
  }


  private String getFileExtension(String mediaType) {
    if (mediaType == null) {
      return ".bin";
    }
    return switch (mediaType.toLowerCase()) {
      case "image/png" -> ".png";
      case "image/gif" -> ".gif";
      case "image/bmp" -> ".bmp";
      case "image/webp" -> ".webp";
      case "image/tiff" -> ".tiff";
      default -> ".jpg";
    };
  }


  /**
   * 替换HTML中的图片引用
   */
  private String replaceImageReferences(String html, String contentId, String imagePath) {
    // 清理Content-ID（移除尖括号）
    var cleanContentId = contentId;
    if (contentId.startsWith("<") && contentId.endsWith(">")) {
      cleanContentId = contentId.substring(1, contentId.length() - 1);
    }
    // 替换各种形式的cid引用
    html = html.replace("cid:" + contentId, imagePath);
    html = html.replace("cid:" + cleanContentId, imagePath);
    // 使用正则表达式替换src属性中的cid引用
    Pattern pattern = Pattern.compile("src=[\"']cid:" + Pattern.quote(cleanContentId) + "[\"']",
        Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(html);
    html = matcher.replaceAll("src=\"" + imagePath + "\"");
    return html;
  }

  /**
   * 检查附件是否是图片类型
   */
  private boolean isImageAttachment(Attachment att) {
    if (att.getContentType() == null) {
      return false;
    }
    String mediaType = att.getContentType().getMediaType().toLowerCase();
    return mediaType.startsWith("image/");
  }

  /**
   * 检查附件是否是内联附件
   */
  private boolean isInlineAttachment(Attachment att) {
    return att.getContentDisposition() != null && att.getContentDisposition().getInline();
  }

  /**
   * HTML转义
   */
  private String escapeHtml(String text) {
    if (text == null) {
      return "";
    }
    return text.replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace("\"", "&quot;")
        .replace("'", "&#39;");
  }

  /**
   * 邮件处理结果类
   */
  private static class EmailProcessResult {

    String processedHtml;
    String attachmentHtml;
    int imageCount;
    int attachmentCount;
    long totalAttachmentSize;

    EmailProcessResult(String processedHtml, String attachmentHtml, int imageCount,
        int attachmentCount, long totalAttachmentSize) {
      this.processedHtml = processedHtml;
      this.attachmentHtml = attachmentHtml;
      this.imageCount = imageCount;
      this.attachmentCount = attachmentCount;
      this.totalAttachmentSize = totalAttachmentSize;
    }
  }

  /**
   * 图片处理结果类
   */
  private static class ImageProcessResult {

    String fileName;
    String imagePath;
    String relativeImagePath;
    String contentId;
    boolean success;
    String errorMessage;

    ImageProcessResult(String fileName, String imagePath, String relativeImagePath,
        String contentId, boolean success, String errorMessage) {
      this.fileName = fileName;
      this.imagePath = imagePath;
      this.relativeImagePath = relativeImagePath;
      this.contentId = contentId;
      this.success = success;
      this.errorMessage = errorMessage;
    }
  }

  /**
   * 附件处理结果类
   */
  private static class AttachmentProcessResult {

    String fileName;
    String filePath;
    String relativeFilePath;
    long fileSize;
    String attachmentHtml;
    boolean success;
    String errorMessage;

    AttachmentProcessResult(String fileName, String filePath, String relativeFilePath,
        long fileSize, String attachmentHtml, boolean success, String errorMessage) {
      this.fileName = fileName;
      this.filePath = filePath;
      this.relativeFilePath = relativeFilePath;
      this.fileSize = fileSize;
      this.attachmentHtml = attachmentHtml;
      this.success = success;
      this.errorMessage = errorMessage;
    }
  }

  /**
   * 统一处理邮件内容（图片和附件）- 模板版本
   */
  private EmailProcessResult processCompleteEmailWithTemplate(MailMessage mailMsg, String bodyHtml,
      String resourceRequestPrefix,
      String imagesDir, String attachmentDir) {
    String processedHtml = bodyHtml != null ? bodyHtml : "";
    StringBuilder attachmentHtml = new StringBuilder();
    int imageCounter = 1;
    int attachmentCounter = 0;
    long totalAttachmentSize = 0;
    // 用于跟踪已处理的Content-ID
    Set<String> processedContentIds = new HashSet<>();

    try {
      // 1. 处理AlternateViews中的LinkedResources（内嵌图片）
      List<LinkedResource> linkedResources = new ArrayList<>();
      for (int i = 0; i < mailMsg.getAlternateViews().size(); i++) {
        AlternateView view = mailMsg.getAlternateViews().get_Item(i);
        if (view.getContentType().getMediaType().toLowerCase().contains("html")) {
          for (int j = 0; j < view.getLinkedResources().size(); j++) {
            linkedResources.add(view.getLinkedResources().get_Item(j));
          }
        }
      }

      // 保存LinkedResource图片
      List<ImageProcessResult> linkedImageResults = saveLinkedResourceImages(
          resourceRequestPrefix,
          linkedResources, imagesDir, imageCounter);
      for (ImageProcessResult result : linkedImageResults) {
        if (result.success) {
          processedHtml = replaceImageReferences(processedHtml, result.contentId,
              result.relativeImagePath);
          processedContentIds.add(result.contentId);
          imageCounter++;
        }
      }

      // 2. 先收集HTML中的所有cid引用
      Set<String> htmlCidReferences = extractCidReferencesFromHtml(processedHtml);

      // 3. 分类处理附件
      List<Attachment> imageAttachments = new ArrayList<>();
      List<Attachment> regularAttachments = new ArrayList<>();

      if (mailMsg.getAttachments().size() > 0) {
        for (int i = 0; i < mailMsg.getAttachments().size(); i++) {
          var att = mailMsg.getAttachments().get_Item(i);
          if (att.getContentType() != null && att.getContentType().toString()
              .contains("application/pkcs7-signature")) {
            continue;
          }

          boolean attIsImageFile = isImageAttachment(att);
          boolean attIsInlineAtt = isInlineAttachment(att);
          boolean isReferencedInHtml = isAttachmentReferencedInHtml(att, htmlCidReferences);

          // 检查是否是内联图片或被HTML引用的图片
          if ((attIsImageFile && attIsInlineAtt) || isReferencedInHtml) {
            imageAttachments.add(att);
          } else {
            regularAttachments.add(att);
          }
        }
      }

      // 4. 保存图片附件
      List<ImageProcessResult> imageResults = saveImageFiles(imageAttachments,
          resourceRequestPrefix, imagesDir,
          htmlCidReferences, imageCounter);
      for (ImageProcessResult result : imageResults) {
        if (result.success) {
          // 替换HTML中的图片引用
          if (isAttachmentReferencedInHtml(
              getAttachmentByContentId(imageAttachments, result.contentId), htmlCidReferences)) {
            // 对于被HTML引用的图片，需要替换所有匹配的cid引用
            for (String cidRef : htmlCidReferences) {
              if (isAttachmentMatchesCid(
                  Objects.requireNonNull(
                      getAttachmentByContentId(imageAttachments, result.contentId)), cidRef)) {
                processedHtml = processedHtml.replace("cid:" + cidRef, result.relativeImagePath);
                processedContentIds.add(cidRef);
              }
            }
          } else if (result.contentId != null && !result.contentId.isEmpty()) {
            processedHtml = replaceImageReferences(processedHtml, result.contentId,
                result.relativeImagePath);
            processedContentIds.add(result.contentId);
          }
          imageCounter++;
        }
      }

      // 5. 保存普通附件
      java.util.List<AttachmentProcessResult> attachmentResults = saveAttachmentFiles(
          resourceRequestPrefix,
          regularAttachments, attachmentDir, attachmentCounter);
      for (AttachmentProcessResult result : attachmentResults) {
        if (result.success) {
          totalAttachmentSize += result.fileSize;
          attachmentHtml.append(result.attachmentHtml);
          attachmentCounter++;
        }
      }

    } catch (Exception e) {
      log.error("处理邮件内容时发生错误: {}", e.getMessage());
    }

    return new EmailProcessResult(processedHtml, attachmentHtml.toString(), imageCounter - 1,
        attachmentCounter, totalAttachmentSize);
  }

  /**
   * 根据Content-ID获取附件
   */
  private Attachment getAttachmentByContentId(java.util.List<Attachment> attachments,
      String contentId) {
    for (Attachment att : attachments) {
      if (contentId != null && contentId.equals(att.getContentId())) {
        return att;
      }
    }
    return null;
  }

  /**
   * 生成单个附件的HTML
   */
  private String generateSingleAttachmentHtml(String attachmentName, String attachmentExt,
      String attachmentSize, String downloadLink) {
    String svgData = getFileIconSvgData(attachmentExt);
    return "省略".formatted(
        svgData, escapeHtml(attachmentName), escapeHtml(attachmentExt), attachmentSize,
        downloadLink);
  }

  /**
   * 根据文件扩展名获取对应的SVG图标的Base64数据
   */
  private String getFileIconSvgData(String fileExt) {
    if (fileExt == null) {
      return getDefaultIconSvgData();
    }

    String ext = fileExt.toLowerCase();

    // Word文档 (.doc, .docx)
    return switch (ext) {
      case ".doc", ".docx" ->"省略";

      // Excel表格 (.xls, .xlsx)
      case ".xls", ".xlsx" ->"省略";

      // PowerPoint演示文稿 (.ppt, .pptx)
      case ".ppt", ".pptx" ->"省略";

      // 压缩文件 (.zip, .rar, .7z)
      case ".zip", ".rar", ".7z" ->"省略";

      // PDF文件 (.pdf)
      case ".pdf" ->"省略";

      // 文本文件 (.txt)
      case ".txt" ->"省略";
      // 图片文件 (.png, .jpg, .jpeg, .gif, .bmp, .webp, .tiff)
      case ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".tiff" -> 省略";
      default ->"省略";
    };

  }

  /**
   * 使用模板生成邮件HTML
   */
  private String generateEmailFromTemplate(String templatePath, String subject, String from,
      String to,
      Date sendDate,
      String content, int attachmentCount, long totalAttachmentSize, String attachmentsList) {
    try {
      // 读取模板文件
      String template = Files.readString(Paths.get(templatePath));
      // 格式化发送时间
      String formattedSendDate = formatSendDate(sendDate);
      // 格式化附件总大小（按要求显示MB/KB）
      String formattedTotalSize = formatFileSizeForTemplate(totalAttachmentSize);
      // 替换模板变量
      template = template.replace("${subject}$", escapeHtml(subject));
      template = template.replace("${from}$", escapeHtml(from));
      template = template.replace("${sendDate}$", formattedSendDate);
      template = template.replace("${to}$", escapeHtml(to));
      template = template.replace("${content}$", content != null ? content : "");
      // template = template.replace("${attachments_total_count}$", String.valueOf(attachmentCount));
      // template = template.replace("${attachments_total_size}$", formattedTotalSize);

      // 如果没有附件，移除附件区域
      if (attachmentCount == 0) {
        // 移除整个附件区域
        template = template.replace("${attachments}$", "");
      } else {
        var attachmentsHtml = "省略".formatted(
            attachmentCount, formattedTotalSize, attachmentsList);
        template = template.replace("${attachments}$", attachmentsHtml);
      }
      return template;
    } catch (Exception e) {
      log.error("读取模板文件失败: {}", e.getMessage());
      return "";
    }
  }

  /**
   * 格式化文件大小（模板专用，按要求显示MB/KB）
   */
  private String formatFileSizeForTemplate(long bytes) {
    if (bytes < 1024 * 1024) {
      // 小于1MB，显示为KB
      return String.format("%.1f KB", bytes / 1024.0);
    } else {
      // 大于等于1MB，显示为MB
      return String.format("%.1f MB", bytes / (1024.0 * 1024));
    }
  }

  /**
   * 从HTML中提取所有cid引用
   */
  private java.util.Set<String> extractCidReferencesFromHtml(String html) {
    java.util.Set<String> cidReferences = new java.util.HashSet<>();
    if (html == null || html.isEmpty()) {
      return cidReferences;
    }
    Pattern cidPattern = Pattern.compile("src=[\"']cid:([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
    Matcher matcher = cidPattern.matcher(html);
    while (matcher.find()) {
      String contentId = matcher.group(1);
      cidReferences.add(contentId);
    }
    return cidReferences;
  }

  /**
   * 检查附件是否被HTML中的cid引用
   */
  private boolean isAttachmentReferencedInHtml(Attachment att,
      java.util.Set<String> htmlCidReferences) {
    for (String cidRef : htmlCidReferences) {
      if (isAttachmentMatchesCid(att, cidRef)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查附件是否匹配特定的cid引用
   */
  private boolean isAttachmentMatchesCid(Attachment att, String cidRef) {
    String contentId = att.getContentId();
    String fileName = att.getName();
    // 直接匹配Content-ID
    if (contentId != null && contentId.equals(cidRef)) {
      return true;
    }
    // 匹配文件名
    if (fileName != null && cidRef.contains(fileName)) {
      return true;
    }
    // 对于PNG文件的特殊匹配
    return fileName != null && fileName.toLowerCase().endsWith(".png") && cidRef.toLowerCase()
        .endsWith(".png");
  }

  /**
   * 从cid引用中提取文件名
   */
  private String extractFileNameFromCidReferences(java.util.Set<String> htmlCidReferences,
      Attachment att) {
    for (String cidRef : htmlCidReferences) {
      if (isAttachmentMatchesCid(att, cidRef)) {
        // 如果cid引用本身就是一个文件名（包含扩展名）
        if (cidRef.contains(".")) {
          // 提取最后一个@符号后的部分，或者整个字符串如果没有@
          String fileName = cidRef;
          int atIndex = cidRef.lastIndexOf("@");
          if (atIndex > 0 && atIndex < cidRef.length() - 1) {
            // 如果@后面的部分包含文件扩展名，使用@前面的部分
            String beforeAt = cidRef.substring(0, atIndex);
            String afterAt = cidRef.substring(atIndex + 1);
            if (beforeAt.contains(".") && !afterAt.contains(".")) {
              fileName = beforeAt;
            } else if (!beforeAt.contains(".") && afterAt.contains(".")) {
              fileName = afterAt;
            } else if (beforeAt.contains(".")) {
              fileName = beforeAt; // 优先使用@前面的部分
            }
          }
          // 清理文件名，移除可能的特殊字符
          fileName = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
          return fileName;
        }
      }
    }
    return null;
  }

  /**
   * 保存图片文件
   *
   * @param imageAttachments  图片附件列表
   * @param imagesDir         图片保存目录
   * @param htmlCidReferences HTML中的cid引用
   * @param imageCounter      图片计数器
   * @return 图片处理结果列表
   */
  private java.util.List<ImageProcessResult> saveImageFiles(
      java.util.List<Attachment> imageAttachments, String resourceRequestPrefix,
      String imagesDir, java.util.Set<String> htmlCidReferences, int imageCounter) {
    java.util.List<ImageProcessResult> results = new java.util.ArrayList<>();

    for (Attachment att : imageAttachments) {
      String attFileName = att.getName();
      String attContentId = att.getContentId();
      boolean isReferencedInHtml = isAttachmentReferencedInHtml(att, htmlCidReferences);

      // 生成图片文件名
      String imageFileName;
      if (isReferencedInHtml) {
        // 从HTML的cid引用中提取文件名
        imageFileName = extractFileNameFromCidReferences(htmlCidReferences, att);
        if (imageFileName == null) {
          // 如果无法从cid提取文件名，则使用原文件名或生成文件名
          if (attFileName != null && attFileName.contains(".")) {
            imageFileName = attFileName;
          } else {
            imageFileName = "image_" + imageCounter + getFileExtension(
                att.getContentType().getMediaType());
          }
        }
      } else {
        // 对于标准内联图片，使用生成的文件名
        if (attFileName != null && attFileName.contains(".")) {
          String originalExt = attFileName.substring(attFileName.lastIndexOf("."));
          imageFileName = "image_" + imageCounter + originalExt;
        } else {
          imageFileName =
              "image_" + imageCounter + getFileExtension(att.getContentType().getMediaType());
        }
      }

      String imagePath = imagesDir + imageFileName;
      String relativeImagePath = resourceRequestPrefix + imageFileName;

      try {
        // 保存图片文件
        att.save(imagePath);
        results.add(new ImageProcessResult(imageFileName, imagePath, relativeImagePath,
            attContentId, true, null));
        imageCounter++;
      } catch (Exception e) {
        String errorMsg = "处理图片失败: " + attFileName + ", 错误: " + e.getMessage();
        log.error(errorMsg);
        results.add(new ImageProcessResult(imageFileName, imagePath, relativeImagePath,
            attContentId, false, errorMsg));
      }
    }

    return results;
  }

  /**
   * 保存LinkedResource图片文件
   *
   * @param linkedResources LinkedResource列表
   * @param imagesDir       图片保存目录
   * @param imageCounter    图片计数器
   * @return 图片处理结果列表
   */
  private java.util.List<ImageProcessResult> saveLinkedResourceImages(String resourceRequestPrefix,
      java.util.List<LinkedResource> linkedResources,
      String imagesDir, int imageCounter) {
    java.util.List<ImageProcessResult> results = new java.util.ArrayList<>();

    for (LinkedResource resource : linkedResources) {
      String contentId = resource.getContentId();
      if (contentId != null && !contentId.isEmpty()) {
        // 生成图片文件名
        String fileExtension = getFileExtension(resource.getContentType().getMediaType());
        String fileName = "image_" + imageCounter + fileExtension;
        String imagePath = imagesDir + fileName;
        String relativeImagePath = resourceRequestPrefix + fileName;

        try {
          // 保存图片文件
          try (FileOutputStream fos = new FileOutputStream(imagePath);
              InputStream is = resource.getContentStream()) {
            copyStream(is, fos);
          }
          results.add(new ImageProcessResult(fileName, imagePath, relativeImagePath,
              contentId, true, null));
          imageCounter++;
        } catch (Exception e) {
          String errorMsg =
              "处理内嵌图片失败 Content-ID: " + contentId + ", 错误: " + e.getMessage();
          log.error(errorMsg);
          results.add(new ImageProcessResult(fileName, imagePath, relativeImagePath,
              contentId, false, errorMsg));
        }
      }
    }
    return results;
  }

  /**
   * 保存附件文件
   *
   * @param attachments       附件列表
   * @param attachmentDir     附件保存目录
   * @param attachmentCounter 附件计数器
   * @return 附件处理结果列表
   */
  private java.util.List<AttachmentProcessResult> saveAttachmentFiles(String resourceRequestPrefix,
      java.util.List<Attachment> attachments,
      String attachmentDir, int attachmentCounter) {
    java.util.List<AttachmentProcessResult> results = new java.util.ArrayList<>();

    for (Attachment att : attachments) {
      String attFileName = att.getName();

      // 生成附件文件名
      if (attFileName == null || attFileName.isEmpty()) {
        attFileName = "attachment_" + (attachmentCounter + 1) + getFileExtension(
            att.getContentType().getMediaType());
      }

      String filePath = attachmentDir + attFileName;
      String relativeFilePath = resourceRequestPrefix + attFileName;

      try {
        // 保存附件文件
        try (FileOutputStream fos = new FileOutputStream(filePath);
            InputStream is = att.getContentStream()) {
          copyStream(is, fos);
        }

        // 获取文件信息
        File savedFile = new File(filePath);
        long fileSize = savedFile.length();
        String fileSizeStr = formatFileSizeForTemplate(fileSize);

        // 获取文件名和扩展名
        String attachmentName = attFileName;
        String attachmentExt = "";
        int lastDotIndex = attFileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < attFileName.length() - 1) {
          attachmentName = attFileName.substring(0, lastDotIndex);
          attachmentExt = attFileName.substring(lastDotIndex);
        }

        // 生成单个附件的HTML（使用模板格式）
        String singleAttachmentHtml = generateSingleAttachmentHtml(attachmentName,
            attachmentExt, fileSizeStr, relativeFilePath);

        results.add(new AttachmentProcessResult(attFileName, filePath, relativeFilePath,
            fileSize, singleAttachmentHtml, true, null));
        attachmentCounter++;
      } catch (Exception e) {
        String errorMsg = "处理附件失败: " + attFileName + ", 错误: " + e.getMessage();
        log.error(errorMsg);
        results.add(new AttachmentProcessResult(attFileName, filePath, relativeFilePath,
            0, "", false, errorMsg));
      }
    }

    return results;
  }
}