package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.service.FileService;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MsgOptionsTest {

    @Mock
    private ArkClient arkClient;

    @Mock
    private FileService fileService;

    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;

    @Mock
    private HtmlOptions htmlOptions;

    private MsgOptions msgOptions;

    @BeforeEach
    void setUp() {
        msgOptions = new MsgOptions(arkClient, fileService, cmsPropertiesConfig, htmlOptions);
    }

    @Test
    void testToTextMarkdown_WithValidMsgFile() throws Exception {
        // 准备测试数据
        Path testMsgFile = Paths.get("src/test/resources/test-email.msg");
        
        // 如果测试文件不存在，跳过测试
        if (!Files.exists(testMsgFile)) {
            System.out.println("Test MSG file not found, skipping test: " + testMsgFile);
            return;
        }

        ToMdParams params = new ToMdParams("test-email.msg", testMsgFile);
        params.setEa("test-ea");
        params.setEmployeeId(12345);
        
        VisCompConfig visCompConfig = new VisCompConfig();
        visCompConfig.setOcr(false);
        params.setVisCompConfig(visCompConfig);

        // 执行测试
        ToMdResult result = msgOptions.toTextMarkdown(params);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-email.msg", result.getFileName());
        assertEquals(1, result.getPageCount());
        assertNotNull(result.getFilePath());
        assertTrue(Files.exists(result.getFilePath()));
        
        // 验证生成的Markdown文件内容
        String content = Files.readString(result.getFilePath());
        assertTrue(content.contains("#")); // 应该包含邮件主题
        assertTrue(content.contains("**发件人**")); // 应该包含发件人信息
        assertTrue(content.contains("**收件人**")); // 应该包含收件人信息
        
        // 清理测试文件
        Files.deleteIfExists(result.getFilePath());
    }

    @Test
    void testToImageMarkdown_WithValidMsgFile() throws Exception {
        // 准备测试数据
        Path testMsgFile = Paths.get("src/test/resources/test-email.msg");
        
        // 如果测试文件不存在，跳过测试
        if (!Files.exists(testMsgFile)) {
            System.out.println("Test MSG file not found, skipping test: " + testMsgFile);
            return;
        }

        ToMdParams params = new ToMdParams("test-email.msg", testMsgFile);
        params.setEa("test-ea");
        params.setEmployeeId(12345);
        
        VisCompConfig visCompConfig = new VisCompConfig();
        visCompConfig.setOcr(true);
        visCompConfig.setDetail(VisCompDetail.LOW);
        params.setVisCompConfig(visCompConfig);

        // Mock OCR相关服务
        when(arkClient.visComps(any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        ToMdResult result = msgOptions.toImageMarkdown(params);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-email.msg", result.getFileName());
        assertEquals(1, result.getPageCount());
        assertNotNull(result.getFilePath());
        assertTrue(Files.exists(result.getFilePath()));
        
        // 验证生成的Markdown文件内容
        String content = Files.readString(result.getFilePath());
        assertTrue(content.contains("#")); // 应该包含邮件主题
        assertTrue(content.contains("**发件人**")); // 应该包含发件人信息
        
        // 清理测试文件
        Files.deleteIfExists(result.getFilePath());
    }

    @Test
    void testToMarkdown_WithOcrDisabled_ShouldCallTextMarkdown() throws Exception {
        // 准备测试数据
        Path testMsgFile = Paths.get("src/test/resources/test-email.msg");
        
        // 如果测试文件不存在，跳过测试
        if (!Files.exists(testMsgFile)) {
            System.out.println("Test MSG file not found, skipping test: " + testMsgFile);
            return;
        }

        ToMdParams params = new ToMdParams("test-email.msg", testMsgFile);
        VisCompConfig visCompConfig = new VisCompConfig();
        visCompConfig.setOcr(false);
        params.setVisCompConfig(visCompConfig);

        // 执行测试
        ToMdResult result = msgOptions.toMarkdown(params);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getUsages()); // 文本模式不应该有Token使用统计
        
        // 清理测试文件
        if (result.getFilePath() != null) {
            Files.deleteIfExists(result.getFilePath());
        }
    }

    @Test
    void testToMarkdown_WithOcrEnabled_ShouldCallImageMarkdown() throws Exception {
        // 准备测试数据
        Path testMsgFile = Paths.get("src/test/resources/test-email.msg");
        
        // 如果测试文件不存在，跳过测试
        if (!Files.exists(testMsgFile)) {
            System.out.println("Test MSG file not found, skipping test: " + testMsgFile);
            return;
        }

        ToMdParams params = new ToMdParams("test-email.msg", testMsgFile);
        VisCompConfig visCompConfig = new VisCompConfig();
        visCompConfig.setOcr(true);
        params.setVisCompConfig(visCompConfig);

        // Mock OCR相关服务
        when(arkClient.visComps(any())).thenReturn(java.util.Collections.emptyList());

        // 执行测试
        ToMdResult result = msgOptions.toMarkdown(params);

        // 验证结果
        assertNotNull(result);
        // OCR模式可能有Token使用统计
        
        // 清理测试文件
        if (result.getFilePath() != null) {
            Files.deleteIfExists(result.getFilePath());
        }
    }
}
