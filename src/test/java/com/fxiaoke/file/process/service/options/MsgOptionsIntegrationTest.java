package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.AsposeInit;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import java.nio.file.Path;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ExtendWith(SpringExtension.class)
public class MsgOptionsIntegrationTest {

  @Autowired
  private MsgOptions msgOptions;

  @BeforeAll
  static void init() {
    AsposeInit.init();
  }

  @Test
  public void testToTextMarkdown_WithValidMsgFile() {
    Path filePath = Path.of("src/test/resources/msg/examples.msg");
    ToMdParams params = new ToMdParams("examples.msg", filePath);
    msgOptions.toTextMarkdown(params);
  }
}
