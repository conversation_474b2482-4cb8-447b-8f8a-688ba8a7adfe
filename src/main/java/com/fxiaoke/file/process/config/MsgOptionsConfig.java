package com.fxiaoke.file.process.config;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.options.HtmlOptions;
import com.fxiaoke.file.process.service.options.MsgOptions;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MsgOptions配置类
 */
@Configuration
public class MsgOptionsConfig {

    @Bean
    public MsgOptions msgOptions(ArkClient arkClient, 
                                FileService fileService, 
                                CmsPropertiesConfig cmsPropertiesConfig,
                                HtmlOptions htmlOptions) {
        return new MsgOptions(arkClient, fileService, cmsPropertiesConfig, htmlOptions);
    }
}
