package com.fxiaoke.file.process.service.options.msg.processor;

import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.options.HtmlOptionsNodeRenderer;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.html2md.converter.HtmlNodeRendererFactory;
import com.vladsch.flexmark.util.data.MutableDataSet;

/**
 * HTML转Markdown处理器，负责HTML内容转换
 */
public class HtmlToMarkdownProcessor {
    
    /**
     * 转换HTML为Markdown
     */
    public static String convertHtmlToMarkdown(String htmlContent, ToMdParams params, FileService fileService) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return "";
        }
        
        MutableDataSet options = createMutableDataSet(false);
        HtmlNodeRendererFactory rendererFactory = createHtmlNodeRendererFactory(params, fileService);
        return convertHtmlToMarkdown(htmlContent, options, rendererFactory);
    }
    
    private static MutableDataSet createMutableDataSet(boolean preserveLineBreaks) {
        MutableDataSet options = new MutableDataSet();
        options.set(FlexmarkHtmlConverter.SKIP_HEADING_1, false);
        options.set(FlexmarkHtmlConverter.SKIP_HEADING_2, false);
        options.set(FlexmarkHtmlConverter.SKIP_ATTRIBUTES, true);
        options.set(FlexmarkHtmlConverter.SKIP_FENCED_CODE, false);
        
        if (preserveLineBreaks) {
            options.set(FlexmarkHtmlConverter.BR_AS_EXTRA_BLANK_LINES, false);
        }
        
        return options;
    }
    
    private static String convertHtmlToMarkdown(String htmlContent, MutableDataSet options, 
                                                HtmlNodeRendererFactory rendererFactory) {
        var builder = FlexmarkHtmlConverter.builder(options);
        if (rendererFactory != null) {
            builder.htmlNodeRendererFactory(rendererFactory);
        }
        return builder.build().convert(htmlContent);
    }
    
    private static HtmlNodeRendererFactory createHtmlNodeRendererFactory(ToMdParams params, FileService fileService) {
        return dataHolder -> new HtmlOptionsNodeRenderer(
                params.getEa(),
                params.getEmployeeId(),
                params.getParentDir(),
                fileService
        );
    }
}
