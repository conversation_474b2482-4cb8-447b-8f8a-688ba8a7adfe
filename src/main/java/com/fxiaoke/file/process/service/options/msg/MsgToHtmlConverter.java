package com.fxiaoke.file.process.service.options.msg;

import com.aspose.email.AlternateView;
import com.aspose.email.AlternateViewCollection;
import com.aspose.email.Attachment;
import com.aspose.email.AttachmentCollection;
import com.aspose.email.HtmlSaveOptions;
import com.aspose.email.LinkedResource;
import com.aspose.email.LinkedResourceCollection;
import com.aspose.email.MailMessage;
import com.aspose.email.ResourceRenderingMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Map;
import java.util.Set;

/**
 * MSG转HTML转换器，负责将MSG文件转换为HTML格式
 * 同时处理内嵌图片和附件的保存
 */
@Slf4j
public class MsgToHtmlConverter {
    
    private static final String EMBEDDED_IMAGE_PREFIX = "embedded_";
    private static final String ATTACHMENT_PREFIX = "attachment_";
    private static final String RESOURCE_FOLDER_NAME = "resources";
    
    private static final Set<String> SUPPORTED_IMAGE_EXTENSIONS = Set.of(
            "jpg", "jpeg", "png", "gif", "webp", "bmp"
    );
    
    private static final Map<String, String> MEDIA_TYPE_TO_EXTENSION = Map.of(
            "image/jpeg", "jpg",
            "image/jpg", "jpg",
            "image/png", "png",
            "image/gif", "gif",
            "image/webp", "webp"
    );
    
    /**
     * 将MSG文件转换为HTML，并保存相关资源
     * 
     * @param mailMessage MSG邮件对象
     * @param outputDir 输出目录
     * @return 转换后的HTML内容
     */
    public static String convertMsgToHtml(MailMessage mailMessage, Path outputDir) {
        try {
            // 创建资源目录
            Path resourceDir = outputDir.resolve(RESOURCE_FOLDER_NAME);
            Files.createDirectories(resourceDir);
            
            // 构建完整的HTML内容
            StringBuilder htmlBuilder = new StringBuilder();
            htmlBuilder.append("<!DOCTYPE html>\n<html>\n<head>\n<meta charset=\"UTF-8\">\n");
            htmlBuilder.append("<title>").append(getSubject(mailMessage)).append("</title>\n");
            htmlBuilder.append("</head>\n<body>\n");
            
            // 添加邮件头信息
            htmlBuilder.append(buildEmailHeaderHtml(mailMessage));
            
            // 处理邮件正文
            String bodyHtml = processEmailBody(mailMessage, resourceDir);
            htmlBuilder.append("<div class=\"email-body\">\n").append(bodyHtml).append("\n</div>\n");
            
            // 处理附件
            String attachmentHtml = processAttachments(mailMessage, resourceDir);
            if (!attachmentHtml.isEmpty()) {
                htmlBuilder.append(attachmentHtml);
            }
            
            htmlBuilder.append("</body>\n</html>");
            
            return htmlBuilder.toString();
            
        } catch (Exception e) {
            log.error("Error converting MSG to HTML", e);
            throw new RuntimeException("Failed to convert MSG to HTML", e);
        }
    }
    
    /**
     * 构建邮件头部HTML
     */
    private static String buildEmailHeaderHtml(MailMessage mailMessage) {
        StringBuilder sb = new StringBuilder();
        sb.append("<div class=\"email-header\">\n");
        
        // 主题
        sb.append("<h1>").append(escapeHtml(getSubject(mailMessage))).append("</h1>\n");
        
        // 发件人
        sb.append("<p><strong>发件人:</strong> ").append(escapeHtml(getFrom(mailMessage))).append("</p>\n");
        
        // 收件人
        sb.append("<p><strong>收件人:</strong> ").append(escapeHtml(getTo(mailMessage))).append("</p>\n");
        
        // 抄送
        String cc = getCc(mailMessage);
        if (!cc.isEmpty()) {
            sb.append("<p><strong>抄送:</strong> ").append(escapeHtml(cc)).append("</p>\n");
        }
        
        // 时间
        sb.append("<p><strong>时间:</strong> ").append(escapeHtml(getDate(mailMessage))).append("</p>\n");
        
        sb.append("</div>\n");
        return sb.toString();
    }
    
    /**
     * 处理邮件正文
     */
    private static String processEmailBody(MailMessage mailMessage, Path resourceDir) throws Exception {
        String htmlBody = mailMessage.getHtmlBody();
        
        if (htmlBody != null && !htmlBody.trim().isEmpty()) {
            // 处理HTML正文中的内嵌图片
            return processHtmlBodyWithEmbeddedImages(mailMessage, htmlBody, resourceDir);
        } else {
            // 如果没有HTML正文，使用纯文本正文
            String textBody = mailMessage.getBody();
            if (textBody != null && !textBody.trim().isEmpty()) {
                return "<pre>" + escapeHtml(textBody) + "</pre>";
            }
        }
        
        return "";
    }
    
    /**
     * 处理HTML正文中的内嵌图片
     */
    private static String processHtmlBodyWithEmbeddedImages(MailMessage mailMessage, String htmlBody, Path resourceDir) throws Exception {
        String processedHtml = htmlBody;
        AlternateViewCollection views = mailMessage.getAlternateViews();
        int embeddedIndex = 1;
        
        for (AlternateView view : views) {
            if ("text/html".equals(view.getContentType().getMediaType())) {
                LinkedResourceCollection resources = view.getLinkedResources();
                
                for (LinkedResource resource : resources) {
                    if (isImageResource(resource)) {
                        String contentId = resource.getContentId();
                        String fileName = String.format("%s%d.%s", 
                                EMBEDDED_IMAGE_PREFIX, embeddedIndex++, getImageExtension(resource));
                        Path imagePath = resourceDir.resolve(fileName);
                        
                        // 保存图片到本地
                        try (InputStream stream = resource.getContentStream()) {
                            Files.copy(stream, imagePath, StandardCopyOption.REPLACE_EXISTING);
                        }
                        
                        // 替换HTML中的CID引用为相对路径
                        if (contentId != null) {
                            String relativePath = RESOURCE_FOLDER_NAME + "/" + fileName;
                            processedHtml = processedHtml.replace("cid:" + contentId, relativePath);
                        }
                    }
                }
            }
        }
        
        return processedHtml;
    }
    
    /**
     * 处理附件
     */
    private static String processAttachments(MailMessage mailMessage, Path resourceDir) throws Exception {
        AttachmentCollection attachments = mailMessage.getAttachments();
        if (attachments == null || attachments.size() == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("<div class=\"email-attachments\">\n<h2>附件</h2>\n<ul>\n");
        
        int attachmentIndex = 1;
        for (Attachment attachment : attachments) {
            String originalName = getAttachmentName(attachment);
            String fileName = String.format("%s%d_%s", ATTACHMENT_PREFIX, attachmentIndex++, originalName);
            Path attachmentPath = resourceDir.resolve(fileName);
            
            // 保存附件
            attachment.save(attachmentPath.toString());
            
            // 添加到HTML
            String relativePath = RESOURCE_FOLDER_NAME + "/" + fileName;
            if (isImageFile(originalName)) {
                sb.append("<li>").append(escapeHtml(originalName))
                  .append(" <img src=\"").append(relativePath).append("\" alt=\"").append(escapeHtml(originalName)).append("\" style=\"max-width: 200px;\"></li>\n");
            } else {
                sb.append("<li><a href=\"").append(relativePath).append("\">").append(escapeHtml(originalName)).append("</a></li>\n");
            }
        }
        
        sb.append("</ul>\n</div>\n");
        return sb.toString();
    }
    
    // 辅助方法
    private static String getSubject(MailMessage mailMessage) {
        return mailMessage.getSubject() != null ? mailMessage.getSubject() : "无主题";
    }
    
    private static String getFrom(MailMessage mailMessage) {
        return mailMessage.getFrom() != null ? mailMessage.getFrom().toString() : "未知";
    }
    
    private static String getTo(MailMessage mailMessage) {
        return mailMessage.getTo() != null ? mailMessage.getTo().toString() : "未知";
    }
    
    private static String getCc(MailMessage mailMessage) {
        return (mailMessage.getCC() != null && mailMessage.getCC().size() > 0) ? 
               mailMessage.getCC().toString() : "";
    }
    
    private static String getDate(MailMessage mailMessage) {
        return mailMessage.getDate() != null ? mailMessage.getDate().toString() : "未知";
    }
    
    private static String getAttachmentName(Attachment attachment) {
        String originalName = attachment.getName();
        return (originalName == null || originalName.trim().isEmpty()) ? "未命名附件" : originalName;
    }
    
    private static boolean isImageResource(LinkedResource resource) {
        String mediaType = resource.getContentType().getMediaType();
        return mediaType != null && mediaType.startsWith("image/");
    }
    
    private static String getImageExtension(LinkedResource resource) {
        String mediaType = resource.getContentType().getMediaType();
        if (mediaType != null) {
            return MEDIA_TYPE_TO_EXTENSION.getOrDefault(mediaType.toLowerCase(), "png");
        }
        return "png";
    }
    
    private static boolean isImageFile(String fileName) {
        if (fileName == null) return false;
        String ext = FilenameUtils.getExtension(fileName).toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.contains(ext);
    }
    
    private static String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }
}
