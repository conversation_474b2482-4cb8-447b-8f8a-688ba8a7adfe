package com.fxiaoke.file.process.service.options.msg;

import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import lombok.Data;

import java.util.List;

/**
 * MSG处理结果，封装处理过程中的数据
 */
@Data
public class MsgProcessingResult {
    
    /**
     * 邮件头部Markdown内容
     */
    private String headerMarkdown;
    
    /**
     * 邮件正文Markdown内容
     */
    private String bodyMarkdown;
    
    /**
     * 附件Markdown内容
     */
    private String attachmentMarkdown;
    
    /**
     * 内嵌图片信息列表（用于OCR处理）
     */
    private List<MarkdownImgInfo> embeddedImages;
    
    /**
     * 获取完整的Markdown内容
     */
    public String getCompleteMarkdown() {
        StringBuilder sb = new StringBuilder();
        
        if (headerMarkdown != null && !headerMarkdown.trim().isEmpty()) {
            sb.append(headerMarkdown);
        }
        
        if (bodyMarkdown != null && !bodyMarkdown.trim().isEmpty()) {
            sb.append("\n## 正文内容\n").append(bodyMarkdown);
        }
        
        if (attachmentMarkdown != null && !attachmentMarkdown.trim().isEmpty()) {
            sb.append("\n").append(attachmentMarkdown);
        }
        
        return sb.toString();
    }
    
    /**
     * 是否有内嵌图片需要OCR处理
     */
    public boolean hasEmbeddedImages() {
        return embeddedImages != null && !embeddedImages.isEmpty();
    }
}
