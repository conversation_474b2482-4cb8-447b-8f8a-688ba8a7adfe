package com.fxiaoke.file.process.service.options.msg;

import com.aspose.email.MailMessage;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.options.msg.processor.AttachmentProcessor;
import com.fxiaoke.file.process.service.options.msg.processor.EmbeddedImageProcessor;
import com.fxiaoke.file.process.service.options.msg.processor.EmailHeaderProcessor;
import com.fxiaoke.file.process.service.options.msg.processor.HtmlToMarkdownProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * MSG转换器，负责协调各个处理器完成转换
 */
@Slf4j
@RequiredArgsConstructor
public class MsgConverter {
    
    private final FileService fileService;
    
    /**
     * 转换为文本模式Markdown（不处理图片OCR）
     */
    public MsgProcessingResult convertToTextMarkdown(MailMessage mailMessage, ToMdParams params) {
        MsgProcessingResult result = new MsgProcessingResult();
        
        // 处理邮件头
        result.setHeaderMarkdown(EmailHeaderProcessor.processEmailHeader(mailMessage));
        
        // 处理纯文本正文
        String textBody = mailMessage.getBody();
        if (textBody != null && !textBody.trim().isEmpty()) {
            result.setBodyMarkdown(textBody);
        }
        
        // 处理附件（文本模式）
        result.setAttachmentMarkdown(AttachmentProcessor.processAttachmentsTextMode(mailMessage.getAttachments()));
        
        // 文本模式不需要内嵌图片处理
        result.setEmbeddedImages(new ArrayList<>());
        
        return result;
    }
    
    /**
     * 转换为图像模式Markdown（包含图片OCR处理）
     */
    public MsgProcessingResult convertToImageMarkdown(MailMessage mailMessage, ToMdParams params) {
        MsgProcessingResult result = new MsgProcessingResult();
        Path outputDir = Paths.get(params.getParentDir());
        
        // 处理邮件头
        result.setHeaderMarkdown(EmailHeaderProcessor.processEmailHeader(mailMessage));
        
        // 处理HTML正文和内嵌图片
        List<MarkdownImgInfo> embeddedImages = new ArrayList<>();
        String htmlContent = EmbeddedImageProcessor.processHtmlBodyWithEmbeddedImages(
                mailMessage, outputDir, embeddedImages);
        
        // 转换HTML为Markdown
        String bodyMarkdown = HtmlToMarkdownProcessor.convertHtmlToMarkdown(htmlContent, params, fileService);
        result.setBodyMarkdown(bodyMarkdown);
        result.setEmbeddedImages(embeddedImages);
        
        // 处理附件（图像模式）
        result.setAttachmentMarkdown(AttachmentProcessor.processAttachments(mailMessage.getAttachments(), outputDir));
        
        log.info("MSG conversion completed, embedded images: {}", embeddedImages.size());
        
        return result;
    }
}
