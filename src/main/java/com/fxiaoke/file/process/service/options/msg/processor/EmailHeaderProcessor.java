package com.fxiaoke.file.process.service.options.msg.processor;

import com.aspose.email.MailAddress;
import com.aspose.email.MailAddressCollection;
import com.aspose.email.MailMessage;

import java.util.Optional;

/**
 * 邮件头处理器，负责提取和格式化邮件头信息
 */
public class EmailHeaderProcessor {
    
    private static final String DEFAULT_SUBJECT = "无主题";
    private static final String DEFAULT_SENDER = "未知";
    private static final String DEFAULT_DATE = "未知";
    
    /**
     * 处理邮件头，返回格式化的Markdown内容
     */
    public static String processEmailHeader(MailMessage mailMessage) {
        StringBuilder sb = new StringBuilder();
        
        // 邮件主题
        sb.append("# ").append(formatSubject(mailMessage.getSubject())).append("\n");
        
        // 发件人
        sb.append("**发件人**: ").append(formatMailAddress(mailMessage.getFrom())).append("\n");
        
        // 收件人
        sb.append("**收件人**: ").append(formatMailAddressCollection(mailMessage.getTo())).append("\n");
        
        // 抄送（如果存在）
        Optional.ofNullable(mailMessage.getCC())
                .filter(cc -> cc.size() > 0)
                .ifPresent(cc -> sb.append("**抄送**: ").append(cc).append("\n"));
        
        // 时间
        sb.append("**时间**: ").append(formatDate(mailMessage.getDate())).append("\n");
        
        return sb.toString();
    }
    
    private static String formatSubject(String subject) {
        return Optional.ofNullable(subject)
                .filter(s -> !s.trim().isEmpty())
                .orElse(DEFAULT_SUBJECT);
    }
    
    private static String formatMailAddress(MailAddress address) {
        return address != null ? address.toString() : DEFAULT_SENDER;
    }
    
    private static String formatMailAddressCollection(MailAddressCollection addresses) {
        return addresses != null ? addresses.toString() : DEFAULT_SENDER;
    }
    
    private static String formatDate(java.util.Date date) {
        return date != null ? date.toString() : DEFAULT_DATE;
    }
}
