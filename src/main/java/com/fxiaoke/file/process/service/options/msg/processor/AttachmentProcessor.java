package com.fxiaoke.file.process.service.options.msg.processor;

import com.aspose.email.Attachment;
import com.aspose.email.AttachmentCollection;
import org.apache.commons.io.FilenameUtils;

import java.nio.file.Path;
import java.util.Set;

/**
 * 附件处理器，负责处理邮件附件
 */
public class AttachmentProcessor {
    
    private static final String MARKDOWN_ATTACHMENT_HEADER = "## 附件";
    private static final String UNNAMED_ATTACHMENT = "未命名附件";
    private static final String ATTACHMENT_PREFIX = "attachment_";
    
    private static final Set<String> SUPPORTED_IMAGE_EXTENSIONS = Set.of(
            "jpg", "jpeg", "png", "gif", "webp", "bmp"
    );
    
    /**
     * 处理附件（图像模式）- 保存附件到本地
     */
    public static String processAttachments(AttachmentCollection attachments, Path outputDir) {
        if (attachments == null || attachments.size() == 0) {
            return "";
        }
        
        StringBuilder markdownBuilder = new StringBuilder();
        markdownBuilder.append(MARKDOWN_ATTACHMENT_HEADER).append("\n");
        
        int attachmentIndex = 1;
        for (Attachment attachment : attachments) {
            processAttachment(attachment, markdownBuilder, outputDir, attachmentIndex++);
        }
        
        return markdownBuilder.toString();
    }
    
    /**
     * 处理附件（文本模式）- 仅列出附件名称
     */
    public static String processAttachmentsTextMode(AttachmentCollection attachments) {
        if (attachments == null || attachments.size() == 0) {
            return "";
        }
        
        StringBuilder markdownBuilder = new StringBuilder();
        markdownBuilder.append(MARKDOWN_ATTACHMENT_HEADER).append("\n");
        
        for (Attachment attachment : attachments) {
            String originalName = getAttachmentName(attachment);
            markdownBuilder.append(String.format("- %s\n", originalName));
        }
        
        return markdownBuilder.toString();
    }
    
    private static void processAttachment(Attachment attachment, StringBuilder markdownBuilder,
                                          Path outputDir, int attachmentIndex) {
        String originalName = getAttachmentName(attachment);
        String fileName = generateAttachmentFileName(originalName, attachmentIndex);
        Path attachmentPath = outputDir.resolve(fileName);
        
        // 保存附件
        attachment.save(attachmentPath.toString());
        
        // 添加到markdown
        appendAttachmentToMarkdown(markdownBuilder, originalName, fileName);
    }
    
    private static String generateAttachmentFileName(String originalName, int attachmentIndex) {
        return String.format("%s%d_%s", ATTACHMENT_PREFIX, attachmentIndex, originalName);
    }
    
    private static void appendAttachmentToMarkdown(StringBuilder markdownBuilder, String originalName, String fileName) {
        if (isImageFile(originalName)) {
            markdownBuilder.append(String.format("- %s ![%s](%s)\n", originalName, originalName, fileName));
        } else {
            markdownBuilder.append(String.format("- %s\n", originalName));
        }
    }
    
    private static String getAttachmentName(Attachment attachment) {
        String originalName = attachment.getName();
        if (originalName == null || originalName.trim().isEmpty()) {
            return UNNAMED_ATTACHMENT;
        }
        return originalName;
    }
    
    private static boolean isImageFile(String fileName) {
        if (fileName == null) return false;
        String ext = FilenameUtils.getExtension(fileName).toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.contains(ext);
    }
}
