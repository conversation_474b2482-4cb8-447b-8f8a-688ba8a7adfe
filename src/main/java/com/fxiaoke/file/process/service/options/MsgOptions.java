package com.fxiaoke.file.process.service.options;

import com.aspose.email.MailMessage;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.options.msg.MsgLoader;
import com.fxiaoke.file.process.service.options.msg.MsgToHtmlConverter;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用于将 MSG 文件转换为 Markdown 格式的选项类。
 *
 * 重构后的版本特点：
 * 1. 复用HtmlOptions：MSG → HTML → 复用HtmlOptions处理流程
 * 2. 架构简化：减少重复代码，提高可维护性
 * 3. 配置优化：使用优化的 MsgLoadOptions 配置
 * 4. 统一处理：图片处理逻辑统一在HtmlOptionsNodeRenderer中
 */
@Component
@Slf4j
public class MsgOptions extends DocOptions {

  private static final String MODULE = "MsgOptions";

  private final HtmlOptions htmlOptions;

  public MsgOptions(ArkClient arkClient,
      FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig,
      HtmlOptions htmlOptions) {
    super(arkClient, fileService, cmsPropertiesConfig);
    this.htmlOptions = htmlOptions;
  }

  /**
   * 文本模式转换：MSG → HTML → 复用HtmlOptions文本模式处理
   *
   * 处理流程：
   * 1. 加载MSG文件（使用优化的配置）
   * 2. 转换MSG为HTML格式
   * 3. 复用HtmlOptions的文本模式处理流程
   */
  @Override
  protected ToMdResult toTextMarkdown(ToMdParams params) {
    try {
      // 1. 加载MSG文件
      MailMessage mailMessage = MsgLoader.loadMsgFile(params.getFilePath());

      // 2. 转换MSG为HTML
      Path outputDir = Paths.get(params.getParentDir());
      String htmlContent = MsgToHtmlConverter.convertMsgToHtml(mailMessage, outputDir);

      // 3. 保存HTML文件
      Path htmlFilePath = saveHtmlFile(params, htmlContent);

      // 4. 复用HtmlOptions的文本模式处理
      ToMdParams htmlParams = createHtmlParams(params, htmlFilePath);
      return htmlOptions.toTextMarkdown(htmlParams);

    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    }
  }

  /**
   * 图像模式转换：MSG → HTML → 复用HtmlOptions图像模式处理
   *
   * 处理流程：
   * 1. 加载MSG文件（使用优化的配置）
   * 2. 转换MSG为HTML格式（包含内嵌图片和附件处理）
   * 3. 复用HtmlOptions的图像模式处理流程（包含OCR）
   */
  @Override
  protected ToMdResult toImageMarkdown(ToMdParams params) {
    try {
      // 1. 加载MSG文件
      MailMessage mailMessage = MsgLoader.loadMsgFile(params.getFilePath());

      // 2. 转换MSG为HTML
      Path outputDir = Paths.get(params.getParentDir());
      String htmlContent = MsgToHtmlConverter.convertMsgToHtml(mailMessage, outputDir);

      // 3. 保存HTML文件
      Path htmlFilePath = saveHtmlFile(params, htmlContent);

      // 4. 复用HtmlOptions的图像模式处理
      ToMdParams htmlParams = createHtmlParams(params, htmlFilePath);
      return htmlOptions.toImageMarkdown(htmlParams);

    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  /**
   * 保存HTML文件
   */
  private Path saveHtmlFile(ToMdParams params, String htmlContent) throws Exception {
    // 生成HTML文件路径（基于原始文件名）
    String originalFileName = params.getFileName();
    String htmlFileName = originalFileName.substring(0, originalFileName.lastIndexOf('.')) + ".html";
    Path htmlFilePath = Paths.get(params.getParentDir()).resolve(htmlFileName);

    Files.writeString(htmlFilePath, htmlContent, StandardCharsets.UTF_8);
    return htmlFilePath;
  }

  /**
   * 创建HTML处理参数
   */
  private ToMdParams createHtmlParams(ToMdParams originalParams, Path htmlFilePath) {
    ToMdParams htmlParams = new ToMdParams(htmlFilePath.getFileName().toString(), htmlFilePath);
    htmlParams.setEa(originalParams.getEa());
    htmlParams.setEmployeeId(originalParams.getEmployeeId());
    htmlParams.setVisCompConfig(originalParams.getVisCompConfig());
    return htmlParams;
  }
}
