package com.fxiaoke.file.process.service.options.msg;

import com.aspose.email.MailMessage;
import com.aspose.email.MsgLoadOptions;
import com.fxiaoke.file.process.domain.exception.BaseException;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;

/**
 * MSG文件加载器，负责配置和加载MSG文件
 */
@Slf4j
public class MsgLoader {
    
    private static final String MODULE = "MsgLoader";
    
    /**
     * 加载MSG文件
     */
    public static MailMessage loadMsgFile(Path filePath) {
        try {
            MsgLoadOptions loadOptions = createLoadOptions();
            return MailMessage.load(filePath.toString(), loadOptions);
        } catch (Exception e) {
            throw new BaseException(e, 400, MODULE + ".loadMsgFile",
                    "MSG file encrypted or corrupted", filePath);
        }
    }
    
    /**
     * 创建MSG加载选项，优化配置
     */
    private static MsgLoadOptions createLoadOptions() {
        MsgLoadOptions loadOptions = new MsgLoadOptions();
        
        // 是否解码提取TNEF附件（通常是winmail.dat文件）
        // 设置为false以解码TNEF附件，获取更多信息
        loadOptions.setPreserveTnefAttachments(false);
        
        // 是否解码签名消息
        loadOptions.setRemoveSignature(false);
        
        // 是否保留原始电子邮件地址
        loadOptions.setKeepOriginalEmailAddresses(true);
        
        // 是否在 MailMessage 中保留 rtf 正文
        loadOptions.setPreserveRtfContent(false);
        
        // 设置转换时格式化的时间,默认 3000 毫秒
        loadOptions.setTimeout(3000);
        
        // 设置加载时是否保持文件流打开,默认 false 关闭
        loadOptions.setLeaveOpen(false);
        
        // 设置首选文本编码,默认 UTF-8
        loadOptions.setPreferredTextEncoding(StandardCharsets.UTF_8);
        
        return loadOptions;
    }
}
