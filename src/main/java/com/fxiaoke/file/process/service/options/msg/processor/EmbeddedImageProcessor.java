package com.fxiaoke.file.process.service.options.msg.processor;

import com.aspose.email.AlternateView;
import com.aspose.email.AlternateViewCollection;
import com.aspose.email.LinkedResource;
import com.aspose.email.LinkedResourceCollection;
import com.aspose.email.MailMessage;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 内嵌图片处理器，负责提取和处理邮件中的内嵌图片
 */
@Slf4j
public class EmbeddedImageProcessor {
    
    private static final String EMBEDDED_IMAGE_PREFIX = "embedded_";
    private static final String CID_PREFIX = "cid:";
    
    private static final Map<String, String> MEDIA_TYPE_TO_EXTENSION = Map.of(
            "image/jpeg", "jpg",
            "image/jpg", "jpg",
            "image/png", "png",
            "image/gif", "gif",
            "image/webp", "webp"
    );
    
    /**
     * 处理HTML正文中的内嵌图片
     */
    public static String processHtmlBodyWithEmbeddedImages(MailMessage mailMessage,
                                                           Path outputDir, List<MarkdownImgInfo> embeddedImages) {
        try {
            String htmlBody = getHtmlBody(mailMessage);
            if (htmlBody.isEmpty()) {
                return htmlBody;
            }
            
            AlternateViewCollection views = mailMessage.getAlternateViews();
            return processAlternateViews(views, htmlBody, outputDir, embeddedImages);
            
        } catch (Exception e) {
            log.error("Error processing embedded images", e);
            return getHtmlBody(mailMessage);
        }
    }
    
    private static String getHtmlBody(MailMessage mailMessage) {
        return Optional.ofNullable(mailMessage.getHtmlBody())
                .filter(body -> !body.trim().isEmpty())
                .orElse(Optional.ofNullable(mailMessage.getBody()).orElse(""));
    }
    
    private static String processAlternateViews(AlternateViewCollection views, String htmlBody,
                                                Path outputDir, List<MarkdownImgInfo> embeddedImages) throws Exception {
        String processedHtml = htmlBody;
        int embeddedIndex = 1;
        
        for (AlternateView view : views) {
            if (isHtmlView(view)) {
                processedHtml = processLinkedResources(view.getLinkedResources(), processedHtml,
                        outputDir, embeddedImages, embeddedIndex);
            }
        }
        
        return processedHtml;
    }
    
    private static boolean isHtmlView(AlternateView view) {
        return "text/html".equals(view.getContentType().getMediaType());
    }
    
    private static String processLinkedResources(LinkedResourceCollection resources, String htmlBody,
                                                 Path outputDir, List<MarkdownImgInfo> embeddedImages,
                                                 int startIndex) throws Exception {
        String processedHtml = htmlBody;
        int embeddedIndex = startIndex;
        
        for (LinkedResource resource : resources) {
            if (isImageResource(resource)) {
                processedHtml = processImageResource(resource, processedHtml, outputDir,
                        embeddedImages, embeddedIndex++);
            }
        }
        
        return processedHtml;
    }
    
    private static String processImageResource(LinkedResource resource, String htmlBody,
                                               Path outputDir, List<MarkdownImgInfo> embeddedImages,
                                               int embeddedIndex) throws Exception {
        String contentId = resource.getContentId();
        String fileName = generateFileName(resource, embeddedIndex);
        Path imagePath = outputDir.resolve(fileName);
        
        // 保存图片到本地
        saveResourceToFile(resource, imagePath);
        
        // 替换HTML中的CID引用
        if (contentId != null) {
            htmlBody = htmlBody.replace(CID_PREFIX + contentId, fileName);
        }
        
        // 创建OCR识别信息
        embeddedImages.add(createMarkdownImgInfo(imagePath, fileName));
        
        return htmlBody;
    }
    
    private static String generateFileName(LinkedResource resource, int embeddedIndex) {
        return String.format("%s%d.%s", EMBEDDED_IMAGE_PREFIX, embeddedIndex, getImageExtension(resource));
    }
    
    private static void saveResourceToFile(LinkedResource resource, Path imagePath) throws Exception {
        try (InputStream stream = resource.getContentStream()) {
            Files.copy(stream, imagePath, StandardCopyOption.REPLACE_EXISTING);
        }
    }
    
    private static MarkdownImgInfo createMarkdownImgInfo(Path imagePath, String fileName) {
        MarkdownImgInfo imgInfo = new MarkdownImgInfo();
        imgInfo.setImagePath(imagePath);
        imgInfo.setOriginalTag(String.format("![%s](%s)", fileName, fileName));
        return imgInfo;
    }
    
    private static boolean isImageResource(LinkedResource resource) {
        String mediaType = resource.getContentType().getMediaType();
        return mediaType != null && mediaType.startsWith("image/");
    }
    
    private static String getImageExtension(LinkedResource resource) {
        String mediaType = resource.getContentType().getMediaType();
        if (mediaType != null) {
            return MEDIA_TYPE_TO_EXTENSION.getOrDefault(mediaType.toLowerCase(), "png");
        }
        return "png";
    }
}
