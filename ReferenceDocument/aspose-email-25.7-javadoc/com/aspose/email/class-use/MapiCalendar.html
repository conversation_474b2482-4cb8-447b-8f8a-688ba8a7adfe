<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MapiCalendar (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MapiCalendar (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiCalendar.html" target="_top">Frames</a></li>
<li><a href="MapiCalendar.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MapiCalendar" class="title">Uses of Class<br>com.aspose.email.MapiCalendar</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createCalendarItem(java.lang.String,%20com.aspose.email.MapiCalendar)">createCalendarItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calId,
                  <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar)</code>
<div class="block">
 Creates MapiCalendar in specified calendar</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#fetchCalendarItem(java.lang.String)">fetchCalendarItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">
 Gets MapiCalendar for specified id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.String)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarUri)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.String,%20java.lang.Iterable)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarUri,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateCalendarItem(com.aspose.email.MapiCalendar)">updateCalendarItem</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar)</code>
<div class="block">
 Updates MapiCalendar</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateCalendarItem(com.aspose.email.MapiCalendar,%20com.aspose.email.UpdateSettings)">updateCalendarItem</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar,
                  <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates appointment</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericList&lt;<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&gt;</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.Iterable)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;calendarUris)</code>
<div class="block">
 Fetch array of <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email"><code>MapiCalendar</code></a> objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericList&lt;<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&gt;</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;calendarUris,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block">
 Fetch array of <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email"><code>MapiCalendar</code></a> objects.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#cancelAppointment(com.aspose.email.MapiCalendar)">cancelAppointment</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;appointment)</code>
<div class="block">
 Cancels appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#cancelAppointment(com.aspose.email.MapiCalendar,%20java.lang.String)">cancelAppointment</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Cancels appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createAppointment(com.aspose.email.MapiCalendar,%20java.lang.String,%20boolean)">createAppointment</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                 boolean&nbsp;suppressInvitations)</code>
<div class="block">
 Creates appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createCalendarItem(java.lang.String,%20com.aspose.email.MapiCalendar)">createCalendarItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calId,
                  <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar)</code>
<div class="block">
 Creates MapiCalendar in specified calendar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiCalendarCollection.html" title="class in com.aspose.email">MapiCalendarCollection</a></code></td>
<td class="colLast"><span class="strong">MapiCalendarCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiCalendarCollection.html#to_MapiCalendarCollection(com.aspose.email.MapiCalendar[])">to_MapiCalendarCollection</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>[]&nbsp;contacts)</code>
<div class="block">
 Converts array to collection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateAppointment(com.aspose.email.MapiCalendar)">updateAppointment</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;appointment)</code>
<div class="block">
 Updates appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateAppointment(com.aspose.email.MapiCalendar,%20java.lang.String)">updateAppointment</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Updates appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateCalendarItem(com.aspose.email.MapiCalendar)">updateCalendarItem</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar)</code>
<div class="block">
 Updates MapiCalendar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateCalendarItem(com.aspose.email.MapiCalendar,%20com.aspose.email.UpdateSettings)">updateCalendarItem</a></strong>(<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&nbsp;mapiCalendar,
                  <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates appointment</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiCalendarCollection.html#MapiCalendarCollection(com.aspose.ms.System.Collections.Generic.IGenericEnumerable)">MapiCalendarCollection</a></strong>(IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&gt;&nbsp;collection)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiCalendarCollection.html" title="class in com.aspose.email"><code>MapiCalendarCollection</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiCalendar.html" target="_top">Frames</a></li>
<li><a href="MapiCalendar.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
