<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ImapFolderInfoCollection (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ImapFolderInfoCollection (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapFolderInfoCollection.html" target="_top">Frames</a></li>
<li><a href="ImapFolderInfoCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ImapFolderInfoCollection" class="title">Uses of Class<br>com.aspose.email.ImapFolderInfoCollection</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endListFolders(com.aspose.ms.System.IAsyncResult)">endListFolders</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the pending asynchronous operation to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapRestoreSettings.</span><code><strong><a href="../../../../com/aspose/email/ImapRestoreSettings.html#getFolders()">getFolders</a></strong>()</code>
<div class="block">
 A folders to be restored.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders()">listFolders</a></strong>()</code>
<div class="block">
 Gets the list of folders in the mailbox</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(boolean)">listFolders</a></strong>(boolean&nbsp;loadFullInfo)</code>
<div class="block">
 Gets the list of folders in the mailbox</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(com.aspose.email.IConnection)">listFolders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection)</code>
<div class="block">
 Gets the list of folders in the mailbox</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(com.aspose.email.IConnection,%20boolean)">listFolders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           boolean&nbsp;loadFullInfo)</code>
<div class="block">
 Gets the list of folders in the mailbox</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(com.aspose.email.IConnection,%20java.lang.String)">listFolders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(com.aspose.email.IConnection,%20java.lang.String,%20boolean)">listFolders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder,
           boolean&nbsp;loadFullInfo)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(com.aspose.email.IConnection,%20java.lang.String,%20boolean,%20int,%20int)">listFolders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder,
           boolean&nbsp;loadFullInfo,
           int&nbsp;options,
           int&nbsp;returnOptions)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(java.lang.String)">listFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(java.lang.String,%20boolean)">listFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder,
           boolean&nbsp;loadFullInfo)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listFolders(java.lang.String,%20boolean,%20int,%20int)">listFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolder,
           boolean&nbsp;loadFullInfo,
           int&nbsp;options,
           int&nbsp;returnOptions)</code>
<div class="block">
 Gets the list of subfolders in the specified folder</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings)">backup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
      <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
      <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings)">backup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
      <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
      <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings)">backup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings)">backup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#backup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.IConnection,%20com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.io.OutputStream,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
           int&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20com.aspose.email.BackupSettings,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           <a href="../../../../com/aspose/email/BackupSettings.html" title="class in com.aspose.email">BackupSettings</a>&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int,%20com.aspose.ms.System.AsyncCallback)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options,
           AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginBackup(com.aspose.email.ImapFolderInfoCollection,%20java.lang.String,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginBackup</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
           int&nbsp;options,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins to backup the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.io.InputStream,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(java.lang.String,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapRestoreSettings.</span><code><strong><a href="../../../../com/aspose/email/ImapRestoreSettings.html#setFolders(com.aspose.email.ImapFolderInfoCollection)">setFolders</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;value)</code>
<div class="block">
 A folders to be restored.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapFolderInfoCollection.html" target="_top">Frames</a></li>
<li><a href="ImapFolderInfoCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
