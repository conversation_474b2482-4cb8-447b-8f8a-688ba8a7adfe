<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.AsposeException (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.AsposeException (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/AsposeException.html" target="_top">Frames</a></li>
<li><a href="AsposeException.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.AsposeException" class="title">Uses of Class<br>com.aspose.email.AsposeException</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">AsposeException</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">AsposeException</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeArgumentException.html" title="class in com.aspose.email">AsposeArgumentException</a></strong></code>
<div class="block">
 The exception that is thrown when one of the arguments provided to a method is not valid.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeArgumentNullException.html" title="class in com.aspose.email">AsposeArgumentNullException</a></strong></code>
<div class="block">
 The ArgumentException is thrown when an argument is null when it shouldn't be.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeArgumentOutOfRangeException.html" title="class in com.aspose.email">AsposeArgumentOutOfRangeException</a></strong></code>
<div class="block">
 The exception that is thrown when one of the arguments provided to a method is out of range.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeBadServerResponceException.html" title="class in com.aspose.email">AsposeBadServerResponceException</a></strong></code>
<div class="block">
 Represents errors that occur during server operation execution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeInvalidDataException.html" title="class in com.aspose.email">AsposeInvalidDataException</a></strong></code>
<div class="block">
 The exception that is thrown when one of the arguments provided to a method is not valid.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeInvalidEnumArgumentException.html" title="class in com.aspose.email">AsposeInvalidEnumArgumentException</a></strong></code>
<div class="block">
 Base exceptioon type for Aspose.Email 
 Represents errors that occur during application execution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeInvalidOperationException.html" title="class in com.aspose.email">AsposeInvalidOperationException</a></strong></code>
<div class="block">
 Exception class for denoting an object was in a state that made calling a method illegal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AsposeNotSupportedException.html" title="class in com.aspose.email">AsposeNotSupportedException</a></strong></code>
<div class="block">
 The exception that is thrown when an invoked method or parameter is not supported, 
 or when there is an attempt to read, seek, or write to a stream that does not support the invoked functionality.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ElementProcessingException.html" title="class in com.aspose.email">ElementProcessingException</a></strong></code>
<div class="block">
 The exception that is thrown when one of many elements failed with exception.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ExchangeException.html" title="class in com.aspose.email">ExchangeException</a></strong></code>
<div class="block">
 Thrown if there is a MS Exchange communication failure.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/FetchTimeoutException.html" title="class in com.aspose.email">FetchTimeoutException</a></strong></code>
<div class="block">
 Represents the exception that is thrown when a message can not be read within the specified time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/FileCorruptedException.html" title="class in com.aspose.email">FileCorruptedException</a></strong></code>
<div class="block">
 Exception that is thrown during file reading,
 when the file appears to be corrupted and impossible to read.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/FormatNotSupportedException.html" title="class in com.aspose.email">FormatNotSupportedException</a></strong></code>
<div class="block">
 Exception that is thrown during document load, 
 when the document format is not 
 recognized or not supported by the component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/GoogleClientException.html" title="class in com.aspose.email">GoogleClientException</a></strong></code>
<div class="block">
 Represents errors that occur during ActiveSync protocol execution.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapException.html" title="class in com.aspose.email">ImapException</a></strong></code>
<div class="block">
 Represents the exception that is thrown when the ImapClient is not able to complete an operation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ObjectDisposedException.html" title="class in com.aspose.email">ObjectDisposedException</a></strong></code>
<div class="block">
 Represents errors that occur when an operation canceled.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/OperationCanceledException.html" title="class in com.aspose.email">OperationCanceledException</a></strong></code>
<div class="block">
 Represents errors that occur when an operation canceled.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Pop3Exception.html" title="class in com.aspose.email">Pop3Exception</a></strong></code>
<div class="block">
 Represents the exception that is thrown when 
 the Pop3Client is not able to complete an operation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ReadLinesTimeoutException.html" title="class in com.aspose.email">ReadLinesTimeoutException</a></strong></code>
<div class="block">
 Represents the exception that is thrown when a response from server can not be read within the specified time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpException.html" title="class in com.aspose.email">SmtpException</a></strong></code>
<div class="block">
 Represents the exception that is thrown when the SmtpClient is not able to complete an operation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpFailedRecipientException.html" title="class in com.aspose.email">SmtpFailedRecipientException</a></strong></code>
<div class="block">
 Represents the exception which arises when the specified recipient is wrong</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpFailedRecipientsException.html" title="class in com.aspose.email">SmtpFailedRecipientsException</a></strong></code>
<div class="block"> 
 Summary description for SmtpFailedRecipientsException.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SSPIException.html" title="class in com.aspose.email">SSPIException</a></strong></code>
<div class="block">
 Represents errors that occur during SSPI execution.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/TimeoutException.html" title="class in com.aspose.email">TimeoutException</a></strong></code>
<div class="block">
 Represents the exception that is thrown when the time for operation has expired.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/TraversalAsposeException.html" title="class in com.aspose.email">TraversalAsposeException</a></strong></code>
<div class="block">
 Represents the exception that can occur when traversing a storage.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">AsposeException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">IgnoreExceptionsCallback.</span><code><strong><a href="../../../../com/aspose/email/IgnoreExceptionsCallback.html#invoke(com.aspose.email.AsposeException,%20java.lang.String)">invoke</a></strong>(<a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">AsposeException</a>&nbsp;ex,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;path)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/AsposeException.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/AsposeException.html" target="_top">Frames</a></li>
<li><a href="AsposeException.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
