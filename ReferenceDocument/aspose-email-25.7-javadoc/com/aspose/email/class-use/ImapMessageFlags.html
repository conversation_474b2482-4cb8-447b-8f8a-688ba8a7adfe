<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ImapMessageFlags (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ImapMessageFlags (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapMessageFlags.html" target="_top">Frames</a></li>
<li><a href="ImapMessageFlags.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ImapMessageFlags" class="title">Uses of Class<br>com.aspose.email.ImapMessageFlags</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseAnd(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">bitwiseAnd</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator &amp;.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">bitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
         <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(com.aspose.email.ImapMessageFlags,%20java.lang.String)">bitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(java.lang.String,%20com.aspose.email.ImapMessageFlags)">bitwiseOr</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;a,
         <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getAnswered()">getAnswered</a></strong>()</code>
<div class="block">
 Message has been answered.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getDeleted()">getDeleted</a></strong>()</code>
<div class="block">
  Message is "deleted" for removal by later EXPUNGE.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getDraft()">getDraft</a></strong>()</code>
<div class="block">
 Message has been marked as a draft.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getEmpty()">getEmpty</a></strong>()</code>
<div class="block">
 Flags are not set</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getFlagged()">getFlagged</a></strong>()</code>
<div class="block">
 Message is "flagged" for urgent/special attention.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfo.html#getFlags()">getFlags</a></strong>()</code>
<div class="block">
 Gets the message flags.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#getRecent()">getRecent</a></strong>()</code>
<div class="block">
 Message is "recently" arrived in this mailbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#isRead()">isRead</a></strong>()</code>
<div class="block">
 Message has been read.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#keyword(java.lang.String)">keyword</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;flag)</code>
<div class="block">
 Message has been marked by custom flag.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseAnd(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_BitwiseAnd</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator &amp;.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_BitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
            <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(com.aspose.email.ImapMessageFlags,%20java.lang.String)">op_BitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(java.lang.String,%20com.aspose.email.ImapMessageFlags)">op_BitwiseOr</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;a,
            <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>[]</code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#split()">split</a></strong>()</code>
<div class="block">
 Split to Array.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#to_ImapMessageFlags(java.lang.String)">to_ImapMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">
 Performs an implicit conversion from <code>long</code> to <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email"><code>ImapMessageFlags</code></a>.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               int&nbsp;sequenceNumber,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               int&nbsp;sequenceNumber,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               int&nbsp;startSequence,
               int&nbsp;endSequence,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               int&nbsp;startSequence,
               int&nbsp;endSequence,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(int&nbsp;sequenceNumber,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(int&nbsp;sequenceNumber,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(int&nbsp;startSequence,
               int&nbsp;endSequence,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(int&nbsp;startSequence,
               int&nbsp;endSequence,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                     <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                     <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                     <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                     <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                     long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                     <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                     <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                     long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(int&nbsp;startSequence,
                    int&nbsp;endSequence,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags to the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               AsyncCallback&nbsp;callback,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence,
                               AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence,
                               AsyncCallback&nbsp;callback,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               AsyncCallback&nbsp;callback,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence,
                               AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                               long&nbsp;modificationSequence,
                               AsyncCallback&nbsp;callback,
                               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          AsyncCallback&nbsp;callback,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence,
                          AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence,
                          AsyncCallback&nbsp;callback,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          AsyncCallback&nbsp;callback,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence,
                          AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                          long&nbsp;modificationSequence,
                          AsyncCallback&nbsp;callback,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(int&nbsp;startSequence,
                       int&nbsp;endSequence,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                                  long&nbsp;modificationSequence,
                                  AsyncCallback&nbsp;callback,
                                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence,
                             AsyncCallback&nbsp;callback,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseAnd(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">bitwiseAnd</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
          <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator &amp;.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">bitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
         <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(com.aspose.email.ImapMessageFlags,%20java.lang.String)">bitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#bitwiseOr(java.lang.String,%20com.aspose.email.ImapMessageFlags)">bitwiseOr</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;a,
         <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                        long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                        long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#equals(com.aspose.email.ImapMessageFlags)">equals</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;other)</code>
<div class="block">
 Determines whether the specified object is equal to the current object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#hasFlag(com.aspose.email.ImapMessageFlags)">hasFlag</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;what)</code>
<div class="block">
 Returns true if "who" contains the "flag"</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#hasFlags(com.aspose.email.ImapMessageFlags)">hasFlags</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Search messages with the specified flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#hasNoFlags(com.aspose.email.ImapMessageFlags)">hasNoFlags</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Search messages with the unspecified flags.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseAnd(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_BitwiseAnd</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator &amp;.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_BitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
            <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(com.aspose.email.ImapMessageFlags,%20java.lang.String)">op_BitwiseOr</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a></code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_BitwiseOr(java.lang.String,%20com.aspose.email.ImapMessageFlags)">op_BitwiseOr</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;a,
            <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator |.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_Equality(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
           <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator ==.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">ImapMessageFlags.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageFlags.html#op_Inequality(com.aspose.email.ImapMessageFlags,%20com.aspose.email.ImapMessageFlags)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;a,
             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;b)</code>
<div class="block">
 Implements the operator !=.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(int,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(int,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(int&nbsp;sequenceNumber,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(int,%20int,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(int&nbsp;startSequence,
                  int&nbsp;endSequence,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.String,%20java.lang.String,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;startUid,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;endUid,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsBySequences(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlagsBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsBySequences(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlagsBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceSet,
                             <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                             long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsByUids(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlagsByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                        long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlagsByUids(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlagsByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uidSet,
                        <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                        long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapMessageFlags.html" target="_top">Frames</a></li>
<li><a href="ImapMessageFlags.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
