<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.HeaderCollection (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.HeaderCollection (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/HeaderCollection.html" target="_top">Frames</a></li>
<li><a href="HeaderCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.HeaderCollection" class="title">Uses of Class<br>com.aspose.email.HeaderCollection</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#endGetMessageHeaders(com.aspose.ms.System.IAsyncResult)">endGetMessageHeaders</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous operation to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">AttachmentBase.</span><code><strong><a href="../../../../com/aspose/email/AttachmentBase.html#getHeaders()">getHeaders</a></strong>()</code>
<div class="block">
 Gets headers collection of attachment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getHeaders()">getHeaders</a></strong>()</code>
<div class="block">
 Gets headers collection of message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#getHeaders()">getHeaders</a></strong>()</code>
<div class="block">
 Gets the transport message headers</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">MboxMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MboxMessageInfo.html#getHeaders()">getHeaders</a></strong>()</code>
<div class="block">
 Gets the collection of header fields</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getHeaders()">getHeaders</a></strong>()</code>
<div class="block">
 Gets the Headers of the E-Mail message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#getMessageHeaders(com.aspose.email.IConnection,%20int)">getMessageHeaders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 int&nbsp;sequenceNumber)</code>
<div class="block">
 Gets the message headers</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#getMessageHeaders(com.aspose.email.IConnection,%20java.lang.String)">getMessageHeaders</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Gets the message headers</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#getMessageHeaders(int)">getMessageHeaders</a></strong>(int&nbsp;sequenceNumber)</code>
<div class="block">
 Gets the message headers</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#getMessageHeaders(java.lang.String)">getMessageHeaders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Gets the message headers</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">HeaderCollection.</span><code><strong><a href="../../../../com/aspose/email/HeaderCollection.html#add(com.aspose.email.HeaderCollection)">add</a></strong>(<a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a>&nbsp;c)</code>
<div class="block">
 Adds a header to collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#setHeaders(com.aspose.email.HeaderCollection)">setHeaders</a></strong>(<a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a>&nbsp;value)</code>
<div class="block">
 Gets the transport message headers</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/HeaderCollection.html#HeaderCollection(com.aspose.email.HeaderCollection)">HeaderCollection</a></strong>(<a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">HeaderCollection</a>&nbsp;col)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email"><code>HeaderCollection</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/HeaderCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/HeaderCollection.html" target="_top">Frames</a></li>
<li><a href="HeaderCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
