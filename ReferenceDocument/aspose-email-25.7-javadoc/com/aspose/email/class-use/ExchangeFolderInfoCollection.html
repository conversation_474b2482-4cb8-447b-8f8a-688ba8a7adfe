<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ExchangeFolderInfoCollection (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ExchangeFolderInfoCollection (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ExchangeFolderInfoCollection.html" target="_top">Frames</a></li>
<li><a href="ExchangeFolderInfoCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ExchangeFolderInfoCollection" class="title">Uses of Class<br>com.aspose.email.ExchangeFolderInfoCollection</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">SyncFolderResult.</span><code><strong><a href="../../../../com/aspose/email/SyncFolderResult.html#getChangedFolders()">getChangedFolders</a></strong>()</code>
<div class="block">
 Collection of changed subfolders in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">RestoreSettings.</span><code><strong><a href="../../../../com/aspose/email/RestoreSettings.html#getFolders()">getFolders</a></strong>()</code>
<div class="block">
 A folders to be restored.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeFolderPageInfo.</span><code><strong><a href="../../../../com/aspose/email/ExchangeFolderPageInfo.html#getItems()">getItems</a></strong>()</code>
<div class="block">
 Gets collection of ExchangeMessageInfo objects</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">SyncFolderResult.</span><code><strong><a href="../../../../com/aspose/email/SyncFolderResult.html#getNewFolders()">getNewFolders</a></strong>()</code>
<div class="block">
 Collection of new subfolders in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#listPublicFolders()">listPublicFolders</a></strong>()</code>
<div class="block">
 Gets collection of public folders from root public folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listPublicFolders()">listPublicFolders</a></strong>()</code>
<div class="block">
 Gets collection of public folders from root public folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#listSubFolders(com.aspose.email.ExchangeFolderInfo)">listSubFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets collection of child public folders from parent</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listSubFolders(com.aspose.email.ExchangeFolderInfo)">listSubFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets collection of child public folders from parent</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#listSubFolders(java.lang.String)">listSubFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri)</code>
<div class="block">
 Gets collection of child folders from parent</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listSubFolders(java.lang.String)">listSubFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri)</code>
<div class="block">
 Gets collection of child folders from parent</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listSubFolders(java.lang.String,%20java.lang.String)">listSubFolders</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri)</code>
<div class="block">
 Gets collection of child folders from parent</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#backup(com.aspose.email.ExchangeFolderInfoCollection,%20java.io.OutputStream,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#backup(com.aspose.email.ExchangeFolderInfoCollection,%20java.io.OutputStream,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#backup(com.aspose.email.ExchangeFolderInfoCollection,%20java.lang.String,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#backup(com.aspose.email.ExchangeFolderInfoCollection,%20java.lang.String,%20int)">backup</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      int&nbsp;options)</code>
<div class="block">
 Backups the content of the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#backupInternal(com.aspose.email.ExchangeFolderInfoCollection,%20com.aspose.ms.System.IO.Stream,%20int)">backupInternal</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
              Stream&nbsp;stream,
              int&nbsp;options)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#deleteFolders(com.aspose.email.ExchangeFolderInfoCollection)">deleteFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders)</code>
<div class="block">
 Deletes the specified folders</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#deleteFolders(com.aspose.email.ExchangeFolderInfoCollection,%20boolean)">deleteFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
             boolean&nbsp;deletePermanently)</code>
<div class="block">
 Deletes the specified folders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restore(java.io.InputStream,%20com.aspose.email.ExchangeFolderInfoCollection,%20int)">restore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
       <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
       int&nbsp;options)</code>
<div class="block">
 Restores the specified exchange folders from the given personal storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restore(com.aspose.email.PersonalStorage,%20com.aspose.email.ExchangeFolderInfoCollection,%20int)">restore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
       <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
       int&nbsp;options)</code>
<div class="block">
 Restores the specified exchange folders from the given personal storage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restore(java.lang.String,%20com.aspose.email.ExchangeFolderInfoCollection,%20int)">restore</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
       <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
       int&nbsp;options)</code>
<div class="block">
 Restores the specified exchange folders from the specified personal storage file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restoreInternal(com.aspose.ms.System.IO.Stream,%20com.aspose.email.ExchangeFolderInfoCollection,%20int)">restoreInternal</a></strong>(Stream&nbsp;stream,
               <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
               int&nbsp;options)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RestoreSettings.</span><code><strong><a href="../../../../com/aspose/email/RestoreSettings.html#setFolders(com.aspose.email.ExchangeFolderInfoCollection)">setFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;value)</code>
<div class="block">
 A folders to be restored.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ExchangeFolderInfoCollection.html" target="_top">Frames</a></li>
<li><a href="ExchangeFolderInfoCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
