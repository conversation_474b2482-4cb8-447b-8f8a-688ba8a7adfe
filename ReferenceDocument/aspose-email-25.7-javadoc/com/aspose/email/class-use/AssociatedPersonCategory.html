<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.AssociatedPersonCategory (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.AssociatedPersonCategory (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/AssociatedPersonCategory.html" target="_top">Frames</a></li>
<li><a href="AssociatedPersonCategory.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.AssociatedPersonCategory" class="title">Uses of Class<br>com.aspose.email.AssociatedPersonCategory</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getAssistant()">getAssistant</a></strong>()</code>
<div class="block">
 Assistant</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getBrother()">getBrother</a></strong>()</code>
<div class="block">
 Brother</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPerson.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPerson.html#getCategory()">getCategory</a></strong>()</code>
<div class="block">
 Gets or sets an object category</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getChild()">getChild</a></strong>()</code>
<div class="block">
 Child</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getCustom()">getCustom</a></strong>()</code>
<div class="block">
 Custom category with empty description</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getDomesticPartner()">getDomesticPartner</a></strong>()</code>
<div class="block">
 Domestic partner</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getFather()">getFather</a></strong>()</code>
<div class="block">
 Father</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getFriend()">getFriend</a></strong>()</code>
<div class="block">
 Friend</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getManager()">getManager</a></strong>()</code>
<div class="block">
 Manager</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getMother()">getMother</a></strong>()</code>
<div class="block">
 Mother</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getParent()">getParent</a></strong>()</code>
<div class="block">
 Parent</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getPartner()">getPartner</a></strong>()</code>
<div class="block">
 Partner</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getReferredBy()">getReferredBy</a></strong>()</code>
<div class="block">
 Referred by</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getRelative()">getRelative</a></strong>()</code>
<div class="block">
 Relative</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getSister()">getSister</a></strong>()</code>
<div class="block">
 Sister</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#getSpouse()">getSpouse</a></strong>()</code>
<div class="block">
 Spouse</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#equals(com.aspose.email.AssociatedPersonCategory)">equals</a></strong>(<a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;other)</code>
<div class="block">
 Determines whether the specified object is equal to the current object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#op_Equality(com.aspose.email.AssociatedPersonCategory,%20com.aspose.email.AssociatedPersonCategory)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;a,
           <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;b)</code>
<div class="block">
 Determines whether the specified objects are equal.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">AssociatedPersonCategory.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPersonCategory.html#op_Inequality(com.aspose.email.AssociatedPersonCategory,%20com.aspose.email.AssociatedPersonCategory)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;a,
             <a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;b)</code>
<div class="block">
 Determines whether the specified objects are not equal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">AssociatedPerson.</span><code><strong><a href="../../../../com/aspose/email/AssociatedPerson.html#setCategory(com.aspose.email.AssociatedPersonCategory)">setCategory</a></strong>(<a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">AssociatedPersonCategory</a>&nbsp;value)</code>
<div class="block">
 Gets or sets an object category</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/AssociatedPersonCategory.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/AssociatedPersonCategory.html" target="_top">Frames</a></li>
<li><a href="AssociatedPersonCategory.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
