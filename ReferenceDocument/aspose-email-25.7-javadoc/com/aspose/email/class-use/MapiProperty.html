<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MapiProperty (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MapiProperty (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiProperty.html" target="_top">Frames</a></li>
<li><a href="MapiProperty.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MapiProperty" class="title">Uses of Class<br>com.aspose.email.MapiProperty</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiNamedProperty.html" title="class in com.aspose.email">MapiNamedProperty</a></strong></code>
<div class="block">
 Represents the data type of Named Property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiObjectProperty.html" title="class in com.aspose.email">MapiObjectProperty</a></strong></code>
<div class="block">
 Represents a Custom object included in Outlook Message documents.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#createMapiPropertyFromBytes(long,%20byte[])">createMapiPropertyFromBytes</a></strong>(long&nbsp;tag,
                           byte[]&nbsp;data)</code>
<div class="block">
 Creates the mapi property from bytes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#createMapiPropertyFromDateTime(long,%20java.util.Date)">createMapiPropertyFromDateTime</a></strong>(long&nbsp;tag,
                              <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;data)</code>
<div class="block">
 Creates the mapi property from date time.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#createMapiPropertyFromLong(long,%20long)">createMapiPropertyFromLong</a></strong>(long&nbsp;tag,
                          long&nbsp;data)</code>
<div class="block">
 Creates the mapi property from long.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#createMapiPropertyFromLong(long,%20long,%20long)">createMapiPropertyFromLong</a></strong>(long&nbsp;tag,
                          long&nbsp;data,
                          long&nbsp;delimiter)</code>
<div class="block">
 Creates the mapi property from long.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#extractProperty(byte[],%20long)">extractProperty</a></strong>(byte[]&nbsp;entryId,
               long&nbsp;tag)</code>
<div class="block">
 Gets the specified property of item, without extract the item fully.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#get_Item(long)">get_Item</a></strong>(long&nbsp;tag)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#get_Item(java.lang.Long)">get_Item</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;arg0)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#get_Item(com.aspose.email.PropertyDescriptor)">get_Item</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiMessageItemBase.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyContainer.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyContainer.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericCollection&lt;? extends <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#get_Values()">get_Values</a></strong>()</code>
<div class="block">
 Gets an System.Collections.Generic.ICollection&lt;MapiProperty&gt; containing the values in the collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerator&lt;KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#iterator()">iterator</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#add(long,%20com.aspose.email.MapiProperty)">add</a></strong>(long&nbsp;key,
   <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Adds a MapiProperty item with specified tag.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#add(com.aspose.email.MapiProperty)">add</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Adds a MapiProperty item with specified tag.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#addCustomProperty(com.aspose.email.MapiProperty,%20java.lang.String)">addCustomProperty</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;stringNameId)</code>
<div class="block">
 Adds the custom property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#addItem(java.lang.Long,%20com.aspose.email.MapiProperty)">addItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;arg0,
       <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;arg1)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiNamedPropertyMappingStorage.</span><code><strong><a href="../../../../com/aspose/email/MapiNamedPropertyMappingStorage.html#addNamedPropertyMapping(com.aspose.email.MapiProperty,%20long,%20java.util.UUID)">addNamedPropertyMapping</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property,
                       long&nbsp;nameId,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</code>
<div class="block">
 Adds the named property mapping for numeric named property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiNamedPropertyMappingStorage.</span><code><strong><a href="../../../../com/aspose/email/MapiNamedPropertyMappingStorage.html#addNamedPropertyMapping(com.aspose.email.MapiProperty,%20java.lang.String,%20java.util.UUID)">addNamedPropertyMapping</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nameId,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</code>
<div class="block">
 Adds the named property mapping for string named property.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#getInstance(com.aspose.email.MapiProperty)">getInstance</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property)</code>
<div class="block">
 Retrieves <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email"><code>PropertyDescriptor</code></a> object from MAPI property</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#set_Item(long,%20com.aspose.email.MapiProperty)">set_Item</a></strong>(long&nbsp;tag,
        <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#set_Item(java.lang.Long,%20com.aspose.email.MapiProperty)">set_Item</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;arg0,
        <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;arg1)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#set_Item(com.aspose.email.PropertyDescriptor,%20com.aspose.email.MapiProperty)">set_Item</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
        <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#setProperty(com.aspose.email.MapiProperty)">setProperty</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Sets the property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyContainer.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyContainer.html#setProperty(com.aspose.email.MapiProperty)">setProperty</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Sets the property.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MessageStore.</span><code><strong><a href="../../../../com/aspose/email/MessageStore.html#setProperty(com.aspose.email.MapiProperty)">setProperty</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property)</code>
<div class="block">
 Sets the property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#tryGetValue(long,%20com.aspose.email.MapiProperty[])">tryGetValue</a></strong>(long&nbsp;key,
           <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>[]&nbsp;value)</code>
<div class="block">
 Gets the property associated with the specified tag.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#add(com.aspose.ms.System.Collections.Generic.KeyValuePair)">add</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;item)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#addItem(com.aspose.ms.System.Collections.Generic.KeyValuePair)">addItem</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;arg0)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#contains(com.aspose.ms.System.Collections.Generic.KeyValuePair)">contains</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;item)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#containsItem(com.aspose.ms.System.Collections.Generic.KeyValuePair)">containsItem</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;arg0)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#remove(com.aspose.ms.System.Collections.Generic.KeyValuePair)">remove</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;item)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#removeItem(com.aspose.ms.System.Collections.Generic.KeyValuePair)">removeItem</a></strong>(KeyValuePair&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>,<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&gt;&nbsp;arg0)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiProperty.html" target="_top">Frames</a></li>
<li><a href="MapiProperty.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
