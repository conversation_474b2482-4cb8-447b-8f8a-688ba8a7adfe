<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MailAddress (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MailAddress (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailAddress.html" target="_top">Frames</a></li>
<li><a href="MailAddress.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MailAddress" class="title">Uses of Class<br>com.aspose.email.MailAddress</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></strong></code>
<div class="block">
 Represents an email address</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#organizerfield">organizerfield</a></strong></code>
<div class="block">Task organizer</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailAddress.</span><code><strong><a href="../../../../com/aspose/email/MailAddress.html#get_Item(int)">get_Item</a></strong>(int&nbsp;i)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#getFederatedDeliveryMailbox()">getFederatedDeliveryMailbox</a></strong>()</code>
<div class="block">
 Gets or sets the mailbox to which a cross-premise message was sent.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getFrom()">getFrom</a></strong>()</code>
<div class="block">
 Gets or sets the from address</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MboxMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MboxMessageInfo.html#getFrom()">getFrom</a></strong>()</code>
<div class="block">
 Gets the from address</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getFrom()">getFrom</a></strong>()</code>
<div class="block">
 Gets the list of authors of this message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">DomainValidatingEventArgs.</span><code><strong><a href="../../../../com/aspose/email/DomainValidatingEventArgs.html#getMailAddress()">getMailAddress</a></strong>()</code>
<div class="block">
 Gets the mail address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#getOrganizer()">getOrganizer</a></strong>()</code>
<div class="block">
 Gets or sets the organizer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#getOrganizer()">getOrganizer</a></strong>()</code>
<div class="block">
 Gets or sets the organizer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">GraphCalendarInfo.</span><code><strong><a href="../../../../com/aspose/email/GraphCalendarInfo.html#getOwner()">getOwner</a></strong>()</code>
<div class="block">
 If set, this represents the user who created or added the calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#getRecipient()">getRecipient</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses of the people who are receiving the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">RecipientTrackingEvent.</span><code><strong><a href="../../../../com/aspose/email/RecipientTrackingEvent.html#getRecipient()">getRecipient</a></strong>()</code>
<div class="block">
 Gets the recipient for whom the event occurred.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailTips.</span><code><strong><a href="../../../../com/aspose/email/MailTips.html#getRecipientAddress()">getRecipientAddress</a></strong>()</code>
<div class="block">
 Gets the mailbox of the recipient.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">GetMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMessageTrackingReportOptions.html#getRecipientFilter()">getRecipientFilter</a></strong>()</code>
<div class="block">
 Gets or sets a recipient address to use with the specified report.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getReversePath()">getReversePath</a></strong>()</code>
<div class="block">
 Gets or sets ReversePath address</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">ClassificationOverride.</span><code><strong><a href="../../../../com/aspose/email/ClassificationOverride.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets or sets email address information of the sender for whom the override is created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail address of the person who is sending the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets or sets sender address</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets the sender of this message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MessageTrackingReport.</span><code><strong><a href="../../../../com/aspose/email/MessageTrackingReport.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets the e-mail address for the sender of the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MessageTrackingReportInfo.</span><code><strong><a href="../../../../com/aspose/email/MessageTrackingReportInfo.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets the e-mail address of the sender for the message that was found.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 Gets or sets the addres from which the response message will be sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">GetMailTipsOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#getSendingAs()">getSendingAs</a></strong>()</code>
<div class="block">
 Gets or sets an e-mail address that a user is trying to send as.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailAddress.</span><code><strong><a href="../../../../com/aspose/email/MailAddress.html#to_MailAddress(com.aspose.email.MailAddressCollection)">to_MailAddress</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;addresses)</code>
<div class="block">
 Performs an implicit conversion from <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email"><code>MailAddressCollection</code></a> to <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailAddress.</span><code><strong><a href="../../../../com/aspose/email/MailAddress.html#to_MailAddress(java.lang.String)">to_MailAddress</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address)</code>
<div class="block">
 Performs an implicit conversion from <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a> to <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">ExchangeDistributionList.</span><code><strong><a href="../../../../com/aspose/email/ExchangeDistributionList.html#toMailAddress()">toMailAddress</a></strong>()</code>
<div class="block">
 Converts the value of the instance to MailAddress.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericCollection&lt;<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&gt;</code></td>
<td class="colLast"><span class="strong">IMessage.</span><code><strong><a href="../../../../com/aspose/email/IMessage.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 Gets BCC recipients</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericCollection&lt;<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&gt;</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getCc()">getCc</a></strong>()</code>
<div class="block">
 Gets CC recipients</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericCollection&lt;<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&gt;</code></td>
<td class="colLast"><span class="strong">IMessage.</span><code><strong><a href="../../../../com/aspose/email/IMessage.html#getCC()">getCC</a></strong>()</code>
<div class="block">
 Gets CC recipients</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericCollection&lt;<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&gt;</code></td>
<td class="colLast"><span class="strong">IMessage.</span><code><strong><a href="../../../../com/aspose/email/IMessage.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Gets recipients</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#addMailAddress(com.aspose.email.MailAddress)">addMailAddress</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;address)</code>
<div class="block">
 Add a <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a> to the collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ClassificationOverride.html" title="class in com.aspose.email">ClassificationOverride</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createOrUpdateOverride(com.aspose.email.MailAddress,%20int)">createOrUpdateOverride</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;sender,
                      int&nbsp;classifyAs)</code>
<div class="block">
 Create an override for a sender identified by an SMTP address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleDeleteFrom(com.aspose.email.MailAddress)">createRuleDeleteFrom</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;from)</code>
<div class="block">
 Creates inbox rule that deletes messages from specified senders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleMoveFrom(com.aspose.email.MailAddress,%20java.lang.String)">createRuleMoveFrom</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;from,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;destinationFolderId)</code>
<div class="block">
 Creates inbox rule that moves messages from specified senders into the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#expandDistributionList(com.aspose.email.MailAddress)">expandDistributionList</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;mailAddress)</code>
<div class="block">
 Expands the public Distribution List members.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#insertItem(int,%20com.aspose.email.MailAddress)">insertItem</a></strong>(int&nbsp;index,
          <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;item)</code>
<div class="block">
 Inserts an element into the <code>System.Collections.ObjectModel.Collection`1</code> at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#setFederatedDeliveryMailbox(com.aspose.email.MailAddress)">setFederatedDeliveryMailbox</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the mailbox to which a cross-premise message was sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setFrom(com.aspose.email.MailAddress)">setFrom</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the from address</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#setItem(int,%20com.aspose.email.MailAddress)">setItem</a></strong>(int&nbsp;index,
       <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;item)</code>
<div class="block">
 Replaces the element at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#setOrganizer(com.aspose.email.MailAddress)">setOrganizer</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the organizer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#setOrganizer(com.aspose.email.MailAddress)">setOrganizer</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the organizer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">GraphCalendarInfo.</span><code><strong><a href="../../../../com/aspose/email/GraphCalendarInfo.html#setOwner(com.aspose.email.MailAddress)">setOwner</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 If set, this represents the user who created or added the calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#setRecipient(com.aspose.email.MailAddress)">setRecipient</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses of the people who are receiving the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">GetMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMessageTrackingReportOptions.html#setRecipientFilter(com.aspose.email.MailAddress)">setRecipientFilter</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets a recipient address to use with the specified report.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setReversePath(com.aspose.email.MailAddress)">setReversePath</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets ReversePath address</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ClassificationOverride.</span><code><strong><a href="../../../../com/aspose/email/ClassificationOverride.html#setSender(com.aspose.email.MailAddress)">setSender</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets email address information of the sender for whom the override is created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FindMessageTrackingReportOptions.</span><code><strong><a href="../../../../com/aspose/email/FindMessageTrackingReportOptions.html#setSender(com.aspose.email.MailAddress)">setSender</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail address of the person who is sending the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setSender(com.aspose.email.MailAddress)">setSender</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets sender address</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#setSender(com.aspose.email.MailAddress)">setSender</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the addres from which the response message will be sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">GetMailTipsOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#setSendingAs(com.aspose.email.MailAddress)">setSendingAs</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;value)</code>
<div class="block">
 Gets or sets an e-mail address that a user is trying to send as.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#to_MailAddressCollection(com.aspose.email.MailAddress)">to_MailAddressCollection</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;address)</code>
<div class="block">
 Performs an implicit conversion from <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a> to <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email"><code>MailAddressCollection</code></a>.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#addRange(java.lang.Iterable)">addRange</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&gt;&nbsp;addresses)</code>
<div class="block">
 Adds addresses to collection</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20java.lang.String)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uid)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20java.lang.String,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uid,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ClassificationOverride.html#ClassificationOverride(com.aspose.email.MailAddress,%20int)">ClassificationOverride</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;sender,
                      int&nbsp;classifyAs)</code>
<div class="block">
 Initalizes static members of class <a href="../../../../com/aspose/email/ClassificationOverride.html" title="class in com.aspose.email"><code>ClassificationOverride</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ClassificationOverride.html#ClassificationOverride(java.lang.String,%20com.aspose.email.MailAddress,%20int)">ClassificationOverride</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
                      <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;sender,
                      int&nbsp;classifyAs)</code>
<div class="block">
 Initalizes static members of class <a href="../../../../com/aspose/email/ClassificationOverride.html" title="class in com.aspose.email"><code>ClassificationOverride</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/DomainValidatingEventArgs.html#DomainValidatingEventArgs(java.lang.String,%20com.aspose.email.MailAddress)">DomainValidatingEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mail,
                         <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;mailaddress)</code>
<div class="block">
 Initializes a new instance of the SyntaxValidatingEventArgs class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#GetMailTipsOptions(com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20int)">GetMailTipsOptions</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;sendingAs,
                  <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
                  int&nbsp;mailTipsRequested)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/GetMailTipsOptions.html" title="class in com.aspose.email"><code>GetMailTipsOptions</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MailMessage.html#MailMessage(com.aspose.email.MailAddress,%20com.aspose.email.MailAddress)">MailMessage</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;from,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;to)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email"><code>MailMessage</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MailServerValidatingEventArgs.html#MailServerValidatingEventArgs(java.lang.String,%20com.aspose.email.MailAddress,%20java.lang.String[])">MailServerValidatingEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mail,
                             <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;mailaddress,
                             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;mailExchangeList)</code>
<div class="block">
 Initializes a new instance of the SyntaxValidatingEventArgs class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailAddress.html" target="_top">Frames</a></li>
<li><a href="MailAddress.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
