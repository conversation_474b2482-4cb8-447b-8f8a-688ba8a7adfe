<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.InboxRule (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.InboxRule (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/InboxRule.html" target="_top">Frames</a></li>
<li><a href="InboxRule.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.InboxRule" class="title">Uses of Class<br>com.aspose.email.InboxRule</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createRule(com.aspose.email.InboxRule)">createRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Create a message rule by specifying a set of conditions and actions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleDeleteContaining(java.lang.String[])">createRuleDeleteContaining</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;filter)</code>
<div class="block">
 Creates inbox rule that deletes messages containing the specified strings in either the body or the subject</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleDeleteFrom(com.aspose.email.MailAddress)">createRuleDeleteFrom</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;from)</code>
<div class="block">
 Creates inbox rule that deletes messages from specified senders</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleMoveContaining(java.lang.String[],%20java.lang.String)">createRuleMoveContaining</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;filter,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;destinationFolderId)</code>
<div class="block">
 Creates inbox rule that moves messages containing the specified strings in either the body or the subject into the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">InboxRule.</span><code><strong><a href="../../../../com/aspose/email/InboxRule.html#createRuleMoveFrom(com.aspose.email.MailAddress,%20java.lang.String)">createRuleMoveFrom</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;from,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;destinationFolderId)</code>
<div class="block">
 Creates inbox rule that moves messages from specified senders into the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#fetchRule(java.lang.String)">fetchRule</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;itemId)</code>
<div class="block">
 Get the properties and relationships of a message rule object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getInboxRules()">getInboxRules</a></strong>()</code>
<div class="block">
 Gets inbox rules</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getInboxRules(java.lang.String)">getInboxRules</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox)</code>
<div class="block">
 Gets inbox rules</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateRule(com.aspose.email.InboxRule)">updateRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Change writable properties on a messageRule object and save the changes.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&gt;</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#listRules()">listRules</a></strong>()</code>
<div class="block">
 Get all the messageRule objects defined for the user's Inbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>List&lt;<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&gt;</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#listRulesInternal()">listRulesInternal</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createInboxRule(com.aspose.email.InboxRule)">createInboxRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Creates the specified inbox rule</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createInboxRule(com.aspose.email.InboxRule,%20java.lang.String)">createInboxRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox)</code>
<div class="block">
 Creates the specified inbox rule</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createRule(com.aspose.email.InboxRule)">createRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Create a message rule by specifying a set of conditions and actions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateInboxRule(com.aspose.email.InboxRule)">updateInboxRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Updates the specified inbox rule</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateInboxRule(com.aspose.email.InboxRule,%20java.lang.String)">updateInboxRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox)</code>
<div class="block">
 Updates the specified inbox rule</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateRule(com.aspose.email.InboxRule)">updateRule</a></strong>(<a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">InboxRule</a>&nbsp;rule)</code>
<div class="block">
 Change writable properties on a messageRule object and save the changes.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/InboxRule.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/InboxRule.html" target="_top">Frames</a></li>
<li><a href="InboxRule.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
