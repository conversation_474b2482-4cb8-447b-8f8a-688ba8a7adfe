<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.EmailAddress (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.EmailAddress (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/EmailAddress.html" target="_top">Frames</a></li>
<li><a href="EmailAddress.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.EmailAddress" class="title">Uses of Class<br>com.aspose.email.EmailAddress</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#get_Item(com.aspose.email.EmailAddressCategory)">get_Item</a></strong>(<a href="../../../../com/aspose/email/EmailAddressCategory.html" title="class in com.aspose.email">EmailAddressCategory</a>&nbsp;category)</code>
<div class="block">
 Default email address for specified category.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#get_Item(int)">get_Item</a></strong>(int&nbsp;index)</code>
<div class="block"> Gets or sets the element at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getEmail()">getEmail</a></strong>()</code>
<div class="block">
 Default email3 address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getEmail1()">getEmail1</a></strong>()</code>
<div class="block">
 Default email1 address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getEmail2()">getEmail2</a></strong>()</code>
<div class="block">
 Default email2 address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getEmail3()">getEmail3</a></strong>()</code>
<div class="block">
 Default email3 address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getHome()">getHome</a></strong>()</code>
<div class="block">
 Default home email address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#getWork()">getWork</a></strong>()</code>
<div class="block">
 Default work email address.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerator&lt;<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&gt;</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#iterator()">iterator</a></strong>()</code>
<div class="block">
 Returns an enumerator that iterates through a collection.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#add(com.aspose.email.EmailAddress)">add</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Adds an item to the list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#addItem(com.aspose.email.EmailAddress)">addItem</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#compareTo(com.aspose.email.EmailAddress)">compareTo</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;obj)</code>
<div class="block">
 Compares the current instance with another object of the same type and returns an integer that indicates whether the current instance precedes, follows, or occurs in the same position in the sort order as the other object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#contains(com.aspose.email.EmailAddress)">contains</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Determines whether the list contains a specific value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#containsItem(com.aspose.email.EmailAddress)">containsItem</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#copyTo(com.aspose.email.EmailAddress[],%20int)">copyTo</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>[]&nbsp;array,
      int&nbsp;index)</code>
<div class="block"> Copies the elements to an array, starting at a particular array index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#copyToTArray(com.aspose.email.EmailAddress[],%20int)">copyToTArray</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>[]&nbsp;array,
            int&nbsp;index)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#equals(com.aspose.email.EmailAddress)">equals</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;obj)</code>
<div class="block">
 Determines whether the specified Object is equal to the current Object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#equals(com.aspose.email.EmailAddress,%20com.aspose.email.EmailAddress)">equals</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;x,
      <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;y)</code>
<div class="block">
 Determines whether the specified object instances are considered equal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#hashCode(com.aspose.email.EmailAddress)">hashCode</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;obj)</code>
<div class="block">
 GetHashCode returns a hash function for specified object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#indexOf(com.aspose.email.EmailAddress)">indexOf</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Determines the index of a specific item in the list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#indexOfItem(com.aspose.email.EmailAddress)">indexOfItem</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#insert(int,%20com.aspose.email.EmailAddress)">insert</a></strong>(int&nbsp;index,
      <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Inserts an item to the list at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#insertItem(int,%20com.aspose.email.EmailAddress)">insertItem</a></strong>(int&nbsp;index,
          <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#op_Equality(com.aspose.email.EmailAddress,%20com.aspose.email.EmailAddress)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;a,
           <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;b)</code>
<div class="block">
 Determines whether the specified objects are equal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">EmailAddress.</span><code><strong><a href="../../../../com/aspose/email/EmailAddress.html#op_Inequality(com.aspose.email.EmailAddress,%20com.aspose.email.EmailAddress)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;a,
             <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;b)</code>
<div class="block">
 Determines whether the specified objects are not equal.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#remove(com.aspose.email.EmailAddress)">remove</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Removes the first occurrence of a specific object from the list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#removeItem(com.aspose.email.EmailAddress)">removeItem</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#set_Item(com.aspose.email.EmailAddressCategory,%20com.aspose.email.EmailAddress)">set_Item</a></strong>(<a href="../../../../com/aspose/email/EmailAddressCategory.html" title="class in com.aspose.email">EmailAddressCategory</a>&nbsp;category,
        <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default email address for specified category.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#set_Item(int,%20com.aspose.email.EmailAddress)">set_Item</a></strong>(int&nbsp;index,
        <a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block"> Gets or sets the element at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setEmail(com.aspose.email.EmailAddress)">setEmail</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default email3 address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setEmail1(com.aspose.email.EmailAddress)">setEmail1</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default email1 address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setEmail2(com.aspose.email.EmailAddress)">setEmail2</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default email2 address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setEmail3(com.aspose.email.EmailAddress)">setEmail3</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default email3 address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setHome(com.aspose.email.EmailAddress)">setHome</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default home email address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailAddressList.</span><code><strong><a href="../../../../com/aspose/email/EmailAddressList.html#setWork(com.aspose.email.EmailAddress)">setWork</a></strong>(<a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">EmailAddress</a>&nbsp;value)</code>
<div class="block">
 Default work email address.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/EmailAddress.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/EmailAddress.html" target="_top">Frames</a></li>
<li><a href="EmailAddress.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
