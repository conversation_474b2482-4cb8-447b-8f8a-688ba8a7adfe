<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MapiPropertyCollection (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MapiPropertyCollection (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiPropertyCollection.html" target="_top">Frames</a></li>
<li><a href="MapiPropertyCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MapiPropertyCollection" class="title">Uses of Class<br>com.aspose.email.MapiPropertyCollection</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#getCustomProperties()">getCustomProperties</a></strong>()</code>
<div class="block">
 Gets collection of custom MapiProperties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">ItemMovedEventArgs.</span><code><strong><a href="../../../../com/aspose/email/ItemMovedEventArgs.html#getItemProperties()">getItemProperties</a></strong>()</code>
<div class="block">
 Gets the item properties that has been moved.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#getNamedProperties()">getNamedProperties</a></strong>()</code>
<div class="block">
 Gets the named properties of message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiMessageItemBase.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#getNamedProperties()">getNamedProperties</a></strong>()</code>
<div class="block">
 Gets the named properties of message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiRecipient.</span><code><strong><a href="../../../../com/aspose/email/MapiRecipient.html#getNamedProperties()">getNamedProperties</a></strong>()</code>
<div class="block">
 Gets the named properties of message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">BaseRestObject.</span><code><strong><a href="../../../../com/aspose/email/BaseRestObject.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the mapi properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the folder properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiObjectProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiObjectProperty.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets a collection of MAPI properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyContainer.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyContainer.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the collection of properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyStream.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyStream.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the collection of properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MessageInfo.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the MessageInfo properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets a mapi properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MessageStore.</span><code><strong><a href="../../../../com/aspose/email/MessageStore.html#getProperties()">getProperties</a></strong>()</code>
<div class="block">
 Gets the MAPI properties of message store object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#getSubStorages()">getSubStorages</a></strong>()</code>
<div class="block">
 Gets the sub storages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiMessageItemBase.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#getSubStorages()">getSubStorages</a></strong>()</code>
<div class="block">
 Gets the sub storages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></code></td>
<td class="colLast"><span class="strong">MapiRecipient.</span><code><strong><a href="../../../../com/aspose/email/MapiRecipient.html#getSubStorages()">getSubStorages</a></strong>()</code>
<div class="block">
 Gets the sub storages.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#changeMessage(java.lang.String,%20com.aspose.email.MapiPropertyCollection)">changeMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryId,
             <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;updatedProperties)</code>
<div class="block">
 Changes the message properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#changeMessages(java.lang.Iterable,%20com.aspose.email.MapiPropertyCollection)">changeMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;entryIdCollection,
              <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;updatedProperties)</code>
<div class="block">
 Changes the messages in folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#changeMessages(com.aspose.email.MapiPropertyCollection)">changeMessages</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;updatedProperties)</code>
<div class="block">
 Changes all messages in folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromProperties(com.aspose.email.MapiPropertyCollection)">fromProperties</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Creates an instance of MapiMessage from a collection of Mapi properties.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/BaseRestObject.html#BaseRestObject(com.aspose.email.MapiPropertyCollection,%20com.aspose.email.PropertyDescriptor[])">BaseRestObject</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties,
              <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>[]&nbsp;explicitProperties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email"><code>FolderInfo</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/GraphFolderInfo.html#GraphFolderInfo(com.aspose.email.MapiPropertyCollection)">GraphFolderInfo</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email"><code>FolderInfo</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ItemMovedEventArgs.html#ItemMovedEventArgs(java.lang.String,%20com.aspose.email.MapiPropertyCollection)">ItemMovedEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryId,
                  <a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ItemMovedEventArgs.html" title="class in com.aspose.email"><code>ItemMovedEventArgs</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactElectronicAddressPropertySet.html#MapiContactElectronicAddressPropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactElectronicAddressPropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactElectronicAddressPropertySet.html" title="class in com.aspose.email"><code>MapiContactElectronicAddressPropertySet</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactEventPropertySet.html#MapiContactEventPropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactEventPropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactEventPropertySet.html" title="class in com.aspose.email"><code>MapiContactEventPropertySet</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactPersonalInfoPropertySet.html#MapiContactPersonalInfoPropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactPersonalInfoPropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactPersonalInfoPropertySet.html" title="class in com.aspose.email"><code>MapiContactPersonalInfoPropertySet</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactPhysicalAddressPropertySet.html#MapiContactPhysicalAddressPropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactPhysicalAddressPropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactPhysicalAddressPropertySet.html" title="class in com.aspose.email"><code>MapiContactPhysicalAddressPropertySet</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactProfessionalPropertySet.html#MapiContactProfessionalPropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactProfessionalPropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactProfessionalPropertySet.html" title="class in com.aspose.email"><code>MapiContactProfessionalPropertySet</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContactTelephonePropertySet.html#MapiContactTelephonePropertySet(com.aspose.email.MapiPropertyCollection)">MapiContactTelephonePropertySet</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiContactTelephonePropertySet.html" title="class in com.aspose.email"><code>MapiContactTelephonePropertySet</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#MapiMessageItemBase(com.aspose.email.MapiPropertyCollection)">MapiMessageItemBase</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email"><code>MapiMessageItemBase</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiPropertyCollection.html" target="_top">Frames</a></li>
<li><a href="MapiPropertyCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
