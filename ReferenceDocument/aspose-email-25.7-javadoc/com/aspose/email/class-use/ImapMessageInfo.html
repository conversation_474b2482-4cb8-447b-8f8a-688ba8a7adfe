<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ImapMessageInfo (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ImapMessageInfo (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapMessageInfo.html" target="_top">Frames</a></li>
<li><a href="ImapMessageInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ImapMessageInfo" class="title">Uses of Class<br>com.aspose.email.ImapMessageInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endListMessage(com.aspose.ms.System.IAsyncResult)">endListMessage</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous ListMessage operation to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]</code></td>
<td class="colLast"><span class="strong">ImapMonitoringEventArgs.</span><code><strong><a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html#getDeletedMessages()">getDeletedMessages</a></strong>()</code>
<div class="block">
 Gets deleted messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]</code></td>
<td class="colLast"><span class="strong">ImapMonitoringEventArgs.</span><code><strong><a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html#getNewMessages()">getNewMessages</a></strong>()</code>
<div class="block">
 Gets new messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(com.aspose.email.IConnection,%20int)">listMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           int&nbsp;sequenceNumber)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(com.aspose.email.IConnection,%20int,%20java.lang.Iterable)">listMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           int&nbsp;sequenceNumber,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;messageExtraFields)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(com.aspose.email.IConnection,%20java.lang.String)">listMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.Iterable)">listMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;messageExtraFields)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(int)">listMessage</a></strong>(int&nbsp;sequenceNumber)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(int,%20java.lang.Iterable)">listMessage</a></strong>(int&nbsp;sequenceNumber,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;messageExtraFields)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(java.lang.String)">listMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessage(java.lang.String,%20java.lang.Iterable)">listMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;messageExtraFields)</code>
<div class="block">
 Gets information about a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#to_(com.aspose.email.ImapMessageInfoCollection)">to_</a></strong>(<a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a>&nbsp;messageInfoCol)</code>
<div class="block">
 Converts collection of ImapMessageInfo to array</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static List&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#to_List(com.aspose.email.ImapMessageInfoCollection)">to_List</a></strong>(<a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a>&nbsp;messageInfoCol)</code>
<div class="block">
 Converts collection of ImapMessageInfo to list</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#add(com.aspose.email.ImapMessageInfo)">add</a></strong>(<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&nbsp;item)</code>
<div class="block">
 Adds the ImapMessageInfo to the ImapMessageCollection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#insert(int,%20com.aspose.email.ImapMessageInfo)">insert</a></strong>(int&nbsp;index,
      <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&nbsp;item)</code>
<div class="block">
 Insert the specified ImapMessagInfo object at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#remove(com.aspose.email.ImapMessageInfo)">remove</a></strong>(<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&nbsp;item)</code>
<div class="block">
 Remove specifed ImapMessageInfo object from this collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#to_ImapMessageInfoCollection(com.aspose.email.ImapMessageInfo[])">to_ImapMessageInfoCollection</a></strong>(<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]&nbsp;messageInfoArr)</code>
<div class="block">
 Converts array of ImapMessageInfo to collection</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#addMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">addMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
               <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
               long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#addRange(java.lang.Iterable)">addRange</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfos)</code>
<div class="block">
 Adds the enumeration of ImapMessageInfo objects to the end of the collection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAddMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAddMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                    <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                    long&nbsp;modificationSequence,
                    AsyncCallback&nbsp;callback,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Adds the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginChangeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginChangeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String)">beginCopyMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback)">beginCopyMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginCopyMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(java.lang.Iterable,%20java.lang.String)">beginCopyMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback)">beginCopyMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginCopyMessages(java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginCopyMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins copy operation asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20boolean)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20boolean)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20boolean)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow)</code>
<div class="block">
 Begins an asynchronous messages deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long,%20boolean)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginDeleteMessages(java.lang.Iterable,%20long,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginDeleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                   long&nbsp;modificationSequence,
                   boolean&nbsp;commitNow,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous message deletion.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20boolean)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginMoveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String,%20boolean)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String,%20boolean,%20com.aspose.ms.System.AsyncCallback)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginMoveMessages(java.lang.Iterable,%20java.lang.String,%20boolean,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginMoveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 boolean&nbsp;commitDeletions,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins an asynchronous moving.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRemoveMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRemoveMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                       <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                       long&nbsp;modificationSequence,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Removes the flags of the message asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#changeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">changeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Changes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#copyMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String)">copyMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Copy messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#copyMessages(java.lang.Iterable,%20java.lang.String)">copyMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Copy messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable)">deleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet)</code>
<div class="block">
 Marks a message with the specified sequence number as deleted</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20boolean)">deleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              boolean&nbsp;commitNow)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted and commits the deletions if user specifies this.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long)">deleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              long&nbsp;modificationSequence)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20long,%20boolean)">deleteMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              long&nbsp;modificationSequence,
              boolean&nbsp;commitNow)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted and commits the deletions if user specifies this.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(java.lang.Iterable)">deleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet)</code>
<div class="block">
 Marks a message with the specified sequence number as deleted</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(java.lang.Iterable,%20boolean)">deleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              boolean&nbsp;commitNow)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted and commits the deletions if user specifies this.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(java.lang.Iterable,%20long)">deleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              long&nbsp;modificationSequence)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#deleteMessages(java.lang.Iterable,%20long,%20boolean)">deleteMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
              long&nbsp;modificationSequence,
              boolean&nbsp;commitNow)</code>
<div class="block">
 Marks a message with the specified unique identifier as deleted and commits the deletions if user specifies this.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#moveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String)">moveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Moves the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#moveMessages(com.aspose.email.IConnection,%20java.lang.Iterable,%20java.lang.String,%20boolean)">moveMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            boolean&nbsp;commitDeletions)</code>
<div class="block">
 Moves the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#moveMessages(java.lang.Iterable,%20java.lang.String)">moveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Moves the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#moveMessages(java.lang.Iterable,%20java.lang.String,%20boolean)">moveMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            boolean&nbsp;commitDeletions)</code>
<div class="block">
 Moves the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#removeMessageFlags(java.lang.Iterable,%20com.aspose.email.ImapMessageFlags,%20long)">removeMessageFlags</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoSet,
                  <a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags,
                  long&nbsp;modificationSequence)</code>
<div class="block">
 Removes the flags of the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapMessageInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#to_ImapMessageInfoCollection(com.aspose.ms.System.Collections.Generic.List)">to_ImapMessageInfoCollection</a></strong>(List&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoLst)</code>
<div class="block">
 Converts list of ImapMessageInfo to collection</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html#ImapMonitoringEventArgs(java.lang.String,%20com.aspose.email.ImapMessageInfo[],%20com.aspose.email.ImapMessageInfo[])">ImapMonitoringEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]&nbsp;newMessages,
                       <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]&nbsp;deletedMessages)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html" title="class in com.aspose.email"><code>ImapMonitoringEventArgs</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html#ImapMonitoringEventArgs(java.lang.String,%20com.aspose.email.ImapMessageInfo[],%20com.aspose.email.ImapMessageInfo[])">ImapMonitoringEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]&nbsp;newMessages,
                       <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>[]&nbsp;deletedMessages)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapMonitoringEventArgs.html" title="class in com.aspose.email"><code>ImapMonitoringEventArgs</code></a> class</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html#ImapMessageInfoCollection(java.lang.Iterable)">ImapMessageInfoCollection</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;messageInfoEn)</code>
<div class="block">
 Initializes a new instance of the ImapMessageCollection class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapMessageInfoCollectionBase.html#ImapMessageInfoCollectionBase(com.aspose.ms.System.Collections.Generic.IGenericList)">ImapMessageInfoCollectionBase</a></strong>(IGenericList&lt;<a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">ImapMessageInfo</a>&gt;&nbsp;list)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;</div>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapMessageInfoCollectionBase.html" title="class in com.aspose.email"><code>ImapMessageInfoCollectionBase</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapMessageInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapMessageInfo.html" target="_top">Frames</a></li>
<li><a href="ImapMessageInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
