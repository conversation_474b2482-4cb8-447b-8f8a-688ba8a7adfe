<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MapiMessageItemBase (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MapiMessageItemBase (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiMessageItemBase.html" target="_top">Frames</a></li>
<li><a href="MapiMessageItemBase.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MapiMessageItemBase" class="title">Uses of Class<br>com.aspose.email.MapiMessageItemBase</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></strong></code>
<div class="block">Represents a MAPI calendar item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email">MapiContact</a></strong></code>
<div class="block">Represents a MAPI contact item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiDistributionList.html" title="class in com.aspose.email">MapiDistributionList</a></strong></code>
<div class="block">Represents a MAPI distribution list item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiJournal.html" title="class in com.aspose.email">MapiJournal</a></strong></code>
<div class="block">Represents a MAPI journal item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></strong></code>
<div class="block">
  Represents an Outlook Message format document that can be parsed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiNote.html" title="class in com.aspose.email">MapiNote</a></strong></code>
<div class="block">Represents a MAPI note ("sticky note") item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiTask.html" title="class in com.aspose.email">MapiTask</a></strong></code>
<div class="block">Represents a MAPI task item.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#addCategory(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">addCategory</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryName)</code>
<div class="block">
 Adds the category for a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#addVotingButton(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">addVotingButton</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;displayName)</code>
<div class="block">
 Adds the voting button.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#archiveItem(java.lang.String,%20com.aspose.email.MapiMessageItemBase)">archiveItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceFolderUri,
           <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;item)</code>
<div class="block">
 The ArchiveItem operation moves an item into the mailbox user's archive mailbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#clearCategories(com.aspose.email.MapiMessageItemBase)">clearCategories</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Clears the categories.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#clearFlag(com.aspose.email.MapiMessageItemBase)">clearFlag</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Clears the follow-up flag and reminder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#clearVotingButtons(com.aspose.email.MapiMessageItemBase)">clearVotingButtons</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Deletes the voting buttons.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createItem(com.aspose.email.MapiMessageItemBase)">createItem</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;item)</code>
<div class="block">
 Creates the given item in the default item folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createItem(java.lang.String,%20com.aspose.email.MapiMessageItemBase)">createItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
          <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;item)</code>
<div class="block">
 Creates the given item in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static IList</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#getCategories(com.aspose.email.MapiMessageItemBase)">getCategories</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Get the available message categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/FollowUpOptions.html" title="class in com.aspose.email">FollowUpOptions</a></code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#getOptions(com.aspose.email.MapiMessageItemBase)">getOptions</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Gets the follow-up options of a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static IGenericList&lt;<a href="../../../../com/aspose/email/UserReaction.html" title="class in com.aspose.email">UserReaction</a>&gt;</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#getReactions(com.aspose.email.MapiMessageItemBase)">getReactions</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Get the available message reactions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static IList</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#getVotingButtons(com.aspose.email.MapiMessageItemBase)">getVotingButtons</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Get the available message voting buttons.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#getVotingButtonsArray(com.aspose.email.MapiMessageItemBase)">getVotingButtonsArray</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Get the available message voting buttons.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#markAsCompleted(com.aspose.email.MapiMessageItemBase)">markAsCompleted</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message)</code>
<div class="block">
 Marks the flagged message as completed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#removeCategory(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">removeCategory</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;categoryName)</code>
<div class="block">
 Removes the category.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#removeVotingButton(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">removeVotingButton</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;displayName)</code>
<div class="block">
 Removes the voting button.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#setFlag(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">setFlag</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;flagRequest)</code>
<div class="block">
 Sets the follow-up flag for a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#setFlag(com.aspose.email.MapiMessageItemBase,%20java.lang.String,%20java.util.Date,%20java.util.Date)">setFlag</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;flagRequest,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;dueDate)</code>
<div class="block">
 Sets the follow-up flag for a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#setFlagForRecipients(com.aspose.email.MapiMessageItemBase,%20java.lang.String)">setFlagForRecipients</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;flagRequest)</code>
<div class="block">
 Sets the flag for a draft message 
 to remind recipients to follow-up.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#setFlagForRecipients(com.aspose.email.MapiMessageItemBase,%20java.lang.String,%20java.util.Date)">setFlagForRecipients</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;flagRequest,
                    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;reminderTime)</code>
<div class="block">
 Sets the flag for a draft message 
 to remind recipients to follow-up.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FollowUpManager.</span><code><strong><a href="../../../../com/aspose/email/FollowUpManager.html#setOptions(com.aspose.email.MapiMessageItemBase,%20com.aspose.email.FollowUpOptions)">setOptions</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;message,
          <a href="../../../../com/aspose/email/FollowUpOptions.html" title="class in com.aspose.email">FollowUpOptions</a>&nbsp;options)</code>
<div class="block">
 Sets the additional follow-up options for a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#updateMessage(java.lang.String,%20com.aspose.email.MapiMessageItemBase)">updateMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryId,
             <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;updatedMessage)</code>
<div class="block">
 Updates the message in folder.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiAttachmentCollection.html#MapiAttachmentCollection(com.aspose.email.MapiMessageItemBase)">MapiAttachmentCollection</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;owner)</code>
<div class="block">
 Initializes a new instance of the MapiAttachmentCollection class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiMessagePropertyStream.html#MapiMessagePropertyStream(com.aspose.email.MapiMessageItemBase)">MapiMessagePropertyStream</a></strong>(<a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">MapiMessageItemBase</a>&nbsp;parent)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiMessagePropertyStream.html" title="class in com.aspose.email"><code>MapiMessagePropertyStream</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiMessageItemBase.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiMessageItemBase.html" target="_top">Frames</a></li>
<li><a href="MapiMessageItemBase.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
