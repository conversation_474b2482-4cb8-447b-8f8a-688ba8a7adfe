<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ContentClassType (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ContentClassType (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ContentClassType.html" target="_top">Frames</a></li>
<li><a href="ContentClassType.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ContentClassType" class="title">Uses of Class<br>com.aspose.email.ContentClassType</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getActivity()">getActivity</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:activity content class that defines 
 a set of properties for an item that is a Journal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getAppointment()">getAppointment</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:appointment content class 
 that defines properties for items that are appointments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getCalendarFolder()">getCalendarFolder</a></strong>()</code>
<div class="block">
 Gets the The urn:content-classes:calendarfolder content class 
 that defines a set of properties for a folder 
 that primarily contains appointment items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getCalendarMessage()">getCalendarMessage</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:calendarmessage content class 
 that defines a set of properties for message items 
 that contain meeting requests.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getContactFolder()">getContactFolder</a></strong>()</code>
<div class="block">
 Gets the The urn:content-classes:contactfolder content class 
 that defines a set of properties for a folder 
 that primarily contains contact items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getDocument()">getDocument</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:document content class that
 defines a set of properties for an item that is a document such as a Microsoft� Word file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getDSN()">getDSN</a></strong>()</code>
<div class="block">
 Gets the the urn:content-classes:dsn content class 
 that defines a set of properties for an item that is a Delivery Status Notification (DSN) message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getFolder()">getFolder</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:folder content class 
 that defines a set of properties for a folder in the Exchange store.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getGroup()">getGroup</a></strong>()</code>
<div class="block">
 Gets the value that represents the content class urn:content-classes:group.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getJournalFolder()">getJournalFolder</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:journalfolder content class 
 that defines a set of properties for a folder that primarily contains journal items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getMailFolder()">getMailFolder</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:mailfolder content class 
 that defines a set of properties for a folder that primarily contains messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getMDN()">getMDN</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:mdn content class 
 that defines a set of properties for an item 
 that is a Mail Delivery Notification (MDN) message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getMessage()">getMessage</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:message content class 
 that defines a set of properties for an item that is a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getNote()">getNote</a></strong>()</code>
<div class="block">
 Gets the value that represents the content class urn:content-classes:note.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getNoteFolder()">getNoteFolder</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:notesfolder content class 
 that defines a set of properties for a folder that primarily contains note items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getPerson()">getPerson</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:person content class 
 that defines a set of properties for an item that is a contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getRecallMesage()">getRecallMesage</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:recallmessage content class 
 that defines a set of properties for a recall message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getRecallReport()">getRecallReport</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:recallreport content class 
 that defines a set of properties for an item that is a recall report message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getReportMessage()">getReportMessage</a></strong>()</code>
<div class="block">
 Gets the value that represents the content class urn:content-classes:reportmessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getTask()">getTask</a></strong>()</code>
<div class="block">
 Gets the value that represents the content class urn:content-classes:task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">ContentClassType</a></code></td>
<td class="colLast"><span class="strong">ContentClassType.</span><code><strong><a href="../../../../com/aspose/email/ContentClassType.html#getTaskFolder()">getTaskFolder</a></strong>()</code>
<div class="block">
 Gets the urn:content-classes:taskfolder content class 
 that defines a set of properties for a folder that primarily contains task items.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ContentClassType.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ContentClassType.html" target="_top">Frames</a></li>
<li><a href="ContentClassType.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
