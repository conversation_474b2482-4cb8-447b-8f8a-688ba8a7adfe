<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:44 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.PropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.PropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.PropertyDescriptor" class="title">Uses of Class<br>com.aspose.email.PropertyDescriptor</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></strong></code>
<div class="block">
 Class contains property description information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></strong></code>
<div class="block">
 Class contains property description information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html" title="class in com.aspose.email">PidTagPropertyDescriptor</a></strong></code>
<div class="block">
 Class contains property description information.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>[]</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(com.aspose.ms.System.Guid...)">find</a></strong>(Guid...&nbsp;propertySets)</code>
<div class="block">
 Finds properties in list according to its PropertySet</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(java.lang.String)">find</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Finds property in list with specified name</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#findOrGetCurrent(com.aspose.email.PropertyDescriptor)">findOrGetCurrent</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 Finds property in list with canonical name</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#get_Item(int)">get_Item</a></strong>(int&nbsp;index)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">MapiProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#getDescriptor()">getDescriptor</a></strong>()</code>
<div class="block">
 Gets descriptor of MAPI property</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#getInstance(com.aspose.email.MapiProperty)">getInstance</a></strong>(<a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;property)</code>
<div class="block">
 Retrieves <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email"><code>PropertyDescriptor</code></a> object from MAPI property</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#parse(java.lang.String)">parse</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;data)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email"><code>PropertyDescriptor</code></a> class</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;</code></td>
<td class="colLast"><span class="strong">SaveResult.</span><code><strong><a href="../../../../com/aspose/email/SaveResult.html#getMissedProperties()">getMissedProperties</a></strong>()</code>
<div class="block">Gets the collection of missed properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerator&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#iterator()">iterator</a></strong>()</code>
<div class="block">
 Returns an enumerator that iterates through the collection.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#add(com.aspose.email.PropertyDescriptor)">add</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 List is read-only.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#contains(com.aspose.email.PropertyDescriptor)">contains</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 Determines whether the System.Collections.Generic.ICollection contains a specific value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#copyTo(com.aspose.email.PropertyDescriptor[],%20int)">copyTo</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>[]&nbsp;array,
      int&nbsp;arrayIndex)</code>
<div class="block">
 List is read-only.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">PidLidPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html#equals(com.aspose.email.PropertyDescriptor)">equals</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;other)</code>
<div class="block">
 Indicates whether the current object is equal to another object of the same type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">PidNamePropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html#equals(com.aspose.email.PropertyDescriptor)">equals</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;other)</code>
<div class="block">
 Indicates whether the current object is equal to another object of the same type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">PidTagPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html#equals(com.aspose.email.PropertyDescriptor)">equals</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;other)</code>
<div class="block">
 Indicates whether the current object is equal to another object of the same type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#equals(com.aspose.email.PropertyDescriptor)">equals</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;other)</code>
<div class="block">
 Indicates whether the current object is equal to another object of the same type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#findOrGetCurrent(com.aspose.email.PropertyDescriptor)">findOrGetCurrent</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 Finds property in list with canonical name</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ComparisonFieldAdapter.html" title="class in com.aspose.email">ComparisonFieldAdapter</a></code></td>
<td class="colLast"><span class="strong">ExtendedPropertiesComparisonField.</span><code><strong><a href="../../../../com/aspose/email/ExtendedPropertiesComparisonField.html#get_Item(com.aspose.email.PropertyDescriptor)">get_Item</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets <a href="../../../../com/aspose/email/ComparisonFieldAdapter.html" title="class in com.aspose.email"><code>ComparisonFieldAdapter</code></a> for <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email"><code>PropertyDescriptor</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#get_Item(com.aspose.email.PropertyDescriptor)">get_Item</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiMessageItemBase.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a></code></td>
<td class="colLast"><span class="strong">MapiPropertyContainer.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyContainer.html#getProperty(com.aspose.email.PropertyDescriptor)">getProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Gets MAPI property by property descriptor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#indexOf(com.aspose.email.PropertyDescriptor)">indexOf</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 Determines the index of a specific item in the System.Collections.Generic.IList.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#insert(int,%20com.aspose.email.PropertyDescriptor)">insert</a></strong>(int&nbsp;index,
      <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 List is read-only.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidLidPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html#op_Equality(com.aspose.email.PidLidPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidNamePropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html#op_Equality(com.aspose.email.PidNamePropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidTagPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html#op_Equality(com.aspose.email.PidTagPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html" title="class in com.aspose.email">PidTagPropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#op_Equality(com.aspose.email.PropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidLidPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html#op_Inequality(com.aspose.email.PidLidPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidNamePropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html#op_Inequality(com.aspose.email.PidNamePropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidTagPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html#op_Inequality(com.aspose.email.PidTagPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PidTagPropertyDescriptor.html" title="class in com.aspose.email">PidTagPropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#op_Inequality(com.aspose.email.PropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#remove(com.aspose.email.PropertyDescriptor)">remove</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;item)</code>
<div class="block">
 List is read-only.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#remove(com.aspose.email.PropertyDescriptor)">remove</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd)</code>
<div class="block">
 Removes the property with the specified property descriptor from the collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#set_Item(int,%20com.aspose.email.PropertyDescriptor)">set_Item</a></strong>(int&nbsp;index,
        <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;value)</code>
<div class="block">
 Gets the element at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#set_Item(com.aspose.email.PropertyDescriptor,%20com.aspose.email.MapiProperty)">set_Item</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
        <a href="../../../../com/aspose/email/MapiProperty.html" title="class in com.aspose.email">MapiProperty</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the value associated with the specified key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiAttachment.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachment.html#setProperty(com.aspose.email.PropertyDescriptor,%20java.lang.Object)">setProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">
 Sets MAPI property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiMessageItemBase.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageItemBase.html#setProperty(com.aspose.email.PropertyDescriptor,%20java.lang.Object)">setProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">
 Sets MAPI property.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="strong">MapiPropertyCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyCollection.html#setProperty(com.aspose.email.PropertyDescriptor,%20java.lang.Object)">setProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">
 Sets MAPI property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiPropertyContainer.</span><code><strong><a href="../../../../com/aspose/email/MapiPropertyContainer.html#setProperty(com.aspose.email.PropertyDescriptor,%20java.lang.Object)">setProperty</a></strong>(<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">
 Sets MAPI property.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email">MapiContact</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchContact(java.lang.String,%20java.lang.Iterable)">fetchContact</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contactUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchItem(java.lang.String,%20java.lang.Iterable)">fetchItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Retrieves the complete item with attachments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericList&lt;<a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a>&gt;</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;calendarUris,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block">
 Fetch array of <a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email"><code>MapiCalendar</code></a> objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiCalendar.html" title="class in com.aspose.email">MapiCalendar</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiCalendar(java.lang.String,%20java.lang.Iterable)">fetchMapiCalendar</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarUri,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessage(java.lang.String,%20java.lang.Iterable)">fetchMapiMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
                <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessages(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uris,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Fetches the speciifed messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiNote.html" title="class in com.aspose.email">MapiNote</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiNote(java.lang.String,%20java.lang.Iterable)">fetchMapiNote</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;noteUri,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericList&lt;<a href="../../../../com/aspose/email/MapiNote.html" title="class in com.aspose.email">MapiNote</a>&gt;</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiNotes(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiNotes</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;noteUris,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block">
 Fetch array of <a href="../../../../com/aspose/email/MapiNote.html" title="class in com.aspose.email"><code>MapiNote</code></a> objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiTask.html" title="class in com.aspose.email">MapiTask</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiTask(java.lang.String,%20java.lang.Iterable)">fetchMapiTask</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;taskUri,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericList&lt;<a href="../../../../com/aspose/email/MapiTask.html" title="class in com.aspose.email">MapiTask</a>&gt;</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiTasks(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiTasks</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;taskUris,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;customProperties)</code>
<div class="block">
 Fetch array of <a href="../../../../com/aspose/email/MapiTask.html" title="class in com.aspose.email"><code>MapiTask</code></a> objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMessage(java.lang.String,%20java.lang.Iterable)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Fetches the message from server</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessageCollection.html" title="class in com.aspose.email">MailMessageCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMessages(java.lang.Iterable,%20java.lang.Iterable)">fetchMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uris,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Fetches the speciifed messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email">MapiContact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listContacts(java.lang.String,%20java.lang.Iterable)">listContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;mapiProperties)</code>
<div class="block">
 Lists contacts located in the specified folder on server</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesByPropertyDescriptor(java.lang.String,%20int,%20java.lang.Iterable)">listMessagesByPropertyDescriptor</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
                                int&nbsp;options,
                                <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 List the messages in the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateNote(java.lang.String,%20com.aspose.email.MapiNote,%20java.lang.Iterable)">updateNote</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
          <a href="../../../../com/aspose/email/MapiNote.html" title="class in com.aspose.email">MapiNote</a>&nbsp;note,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;additionalProperties)</code>
<div class="block">
 Updates the specified note.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateTask(java.lang.String,%20com.aspose.email.MapiTask,%20java.lang.Iterable)">updateTask</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
          <a href="../../../../com/aspose/email/MapiTask.html" title="class in com.aspose.email">MapiTask</a>&nbsp;task,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;additionalProperties)</code>
<div class="block">
 Updates the specified task.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/BaseRestObject.html#BaseRestObject(com.aspose.email.MapiPropertyCollection,%20com.aspose.email.PropertyDescriptor[])">BaseRestObject</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties,
              <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>[]&nbsp;explicitProperties)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email"><code>FolderInfo</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
