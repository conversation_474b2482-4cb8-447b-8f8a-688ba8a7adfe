<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.Appointment (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.Appointment (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Appointment.html" target="_top">Frames</a></li>
<li><a href="Appointment.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.Appointment" class="title">Uses of Class<br>com.aspose.email.Appointment</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#createAppointment(java.lang.String,%20com.aspose.email.Appointment)">createAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Creates an appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchAppointment(java.lang.String)">fetchAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentUri)</code>
<div class="block">
 Fetch the specified appointment from server.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchAppointment(java.lang.String,%20java.lang.String)">fetchAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentUri,
                <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Fetch the specified appointment from server.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#fetchAppointment(java.lang.String,%20java.lang.String)">fetchAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentId)</code>
<div class="block">
 Fetches appointment by identifier.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">CalendarReader.</span><code><strong><a href="../../../../com/aspose/email/CalendarReader.html#getCurrent()">getCurrent</a></strong>()</code>
<div class="block">
 Current read event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#importAppointment(java.lang.String,%20com.aspose.email.Appointment)">importAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Imports appointment to calendar</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#listAppointmentInstances(java.lang.String,%20java.lang.String)">listAppointmentInstances</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentId)</code>
<div class="block">
 Gets list of an appointment instances for calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments()">listAppointments</a></strong>()</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(boolean)">listAppointments</a></strong>(boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(com.aspose.email.MailQuery)">listAppointments</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(com.aspose.email.MailQuery,%20boolean)">listAppointments</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#listAppointments(java.lang.String)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId)</code>
<div class="block">
 Gets list of an appointments for calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String,%20boolean)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String,%20com.aspose.email.MailQuery)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#load(java.io.InputStream)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Loads <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> from the stream</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#load(java.io.InputStream,%20com.aspose.email.AppointmentLoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
    <a href="../../../../com/aspose/email/AppointmentLoadOptions.html" title="class in com.aspose.email">AppointmentLoadOptions</a>&nbsp;options)</code>
<div class="block">
 Loads <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> from the stream</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#load(java.io.InputStream,%20boolean)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
    boolean&nbsp;applyLocalTimeZone)</code>
<div class="block">
 Loads <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> from the stream</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#load(java.lang.String)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filePath)</code>
<div class="block">
 Loads <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> from the file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#load(java.lang.String,%20com.aspose.email.AppointmentLoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filePath,
    <a href="../../../../com/aspose/email/AppointmentLoadOptions.html" title="class in com.aspose.email">AppointmentLoadOptions</a>&nbsp;options)</code>
<div class="block">
 Loads <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> from the file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#moveAppointment(java.lang.String,%20java.lang.String,%20java.lang.String)">moveAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceCalendarId,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;destinationCalendarId,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentId)</code>
<div class="block">
 Moves an appointment to another calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#moveAppointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20boolean)">moveAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceCalendarId,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;destinationCalendarId,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;appointmentId,
               boolean&nbsp;sendNotifications)</code>
<div class="block">
 Moves an appointment to another calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">AppointmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AppointmentCollection.html#to_(com.aspose.email.AppointmentCollection)">to_</a></strong>(<a href="../../../../com/aspose/email/AppointmentCollection.html" title="class in com.aspose.email">AppointmentCollection</a>&nbsp;appointments)</code>
<div class="block">
 Converts collection of appointments to array</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#updateAppointment(java.lang.String,%20com.aspose.email.Appointment)">updateAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Updates an appointment.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&gt;</code></td>
<td class="colLast"><span class="strong">CalendarReader.</span><code><strong><a href="../../../../com/aspose/email/CalendarReader.html#loadAsMultiple()">loadAsMultiple</a></strong>()</code>
<div class="block">
 Loads a list of events from a calendar with multiple events.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static List&lt;<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppointmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AppointmentCollection.html#to_List(com.aspose.email.AppointmentCollection)">to_List</a></strong>(<a href="../../../../com/aspose/email/AppointmentCollection.html" title="class in com.aspose.email">AppointmentCollection</a>&nbsp;appointments)</code>
<div class="block">
 Converts collection of appointments to list</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#archiveItem(java.lang.String,%20com.aspose.email.Appointment)">archiveItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sourceFolderUri,
           <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 The ArchiveItem operation moves an item into the mailbox user's archive mailbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#cancelAppointment(com.aspose.email.Appointment)">cancelAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Cancels appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#cancelAppointment(com.aspose.email.Appointment,%20java.lang.String)">cancelAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Cancels appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createAppointment(com.aspose.email.Appointment)">createAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Creates appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createAppointment(com.aspose.email.Appointment,%20java.lang.String)">createAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Creates appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#createAppointment(java.lang.String,%20com.aspose.email.Appointment)">createAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Creates an appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#importAppointment(java.lang.String,%20com.aspose.email.Appointment)">importAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Imports appointment to calendar</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AppointmentCollection.html" title="class in com.aspose.email">AppointmentCollection</a></code></td>
<td class="colLast"><span class="strong">AppointmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AppointmentCollection.html#to_AppointmentCollection(com.aspose.email.Appointment[])">to_AppointmentCollection</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]&nbsp;appointments)</code>
<div class="block">
 Converts array of appointments to collection</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateAppointment(com.aspose.email.Appointment)">updateAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Updates appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateAppointment(com.aspose.email.Appointment,%20java.lang.String)">updateAppointment</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Updates appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#updateAppointment(java.lang.String,%20com.aspose.email.Appointment)">updateAppointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;calendarId,
                 <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Updates an appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">CalendarWriter.</span><code><strong><a href="../../../../com/aspose/email/CalendarWriter.html#write(com.aspose.email.Appointment)">write</a></strong>(<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&nbsp;appointment)</code>
<div class="block">
 Writes appointment in underlying stream.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/AppointmentCollection.html" title="class in com.aspose.email">AppointmentCollection</a></code></td>
<td class="colLast"><span class="strong">AppointmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AppointmentCollection.html#to_AppointmentCollection(com.aspose.ms.System.Collections.Generic.List)">to_AppointmentCollection</a></strong>(List&lt;<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&gt;&nbsp;appointments)</code>
<div class="block">
 Converts list of appointments to collection</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AppointmentCollection.html#AppointmentCollection(java.lang.Iterable)">AppointmentCollection</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>&gt;&nbsp;appointments)</code>
<div class="block">
 Initializes a new instance of the AppointmentCollection class</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Appointment.html" target="_top">Frames</a></li>
<li><a href="Appointment.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
