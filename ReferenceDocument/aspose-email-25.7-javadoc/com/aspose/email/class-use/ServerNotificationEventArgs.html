<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:45 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ServerNotificationEventArgs (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ServerNotificationEventArgs (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ServerNotificationEventArgs.html" target="_top">Frames</a></li>
<li><a href="ServerNotificationEventArgs.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ServerNotificationEventArgs" class="title">Uses of Class<br>com.aspose.email.ServerNotificationEventArgs</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type parameters of type <a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#CalendarFolderServerNotifications">CalendarFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Calendar folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#CalendarFolderServerNotificationsDelegate">CalendarFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Calendar folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#ContactsFolderServerNotifications">ContactsFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Contacts folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#ContactsFolderServerNotificationsDelegate">ContactsFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Contacts folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#DeletedItemsFolderServerNotifications">DeletedItemsFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for DeletedItems folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#DeletedItemsFolderServerNotificationsDelegate">DeletedItemsFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for DeletedItems folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#DraftsFolderServerNotifications">DraftsFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Drafts folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#DraftsFolderServerNotificationsDelegate">DraftsFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Drafts folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#InboxFolderServerNotifications">InboxFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Inbox folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#InboxFolderServerNotificationsDelegate">InboxFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Inbox folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#JournalFolderServerNotifications">JournalFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Journal folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#JournalFolderServerNotificationsDelegate">JournalFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Journal folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#NotesFolderServerNotifications">NotesFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Notes folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#NotesFolderServerNotificationsDelegate">NotesFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Notes folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#OutboxFolderServerNotifications">OutboxFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Outbox folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#OutboxFolderServerNotificationsDelegate">OutboxFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Outbox folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#RootFolderServerNotifications">RootFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Root folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#RootFolderServerNotificationsDelegate">RootFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Root folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#SentItemsFolderServerNotifications">SentItemsFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for SentItems folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#SentItemsFolderServerNotificationsDelegate">SentItemsFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for SentItems folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Event&lt;EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#TasksFolderServerNotifications">TasksFolderServerNotifications</a></strong></code>
<div class="block">Occurs when arises specified event type for Tasks folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected EventHandler&lt;<a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">ServerNotificationEventArgs</a>&gt;</code></td>
<td class="colLast"><span class="strong">EWSClient.</span><code><strong><a href="../../../../com/aspose/email/EWSClient.html#TasksFolderServerNotificationsDelegate">TasksFolderServerNotificationsDelegate</a></strong></code>
<div class="block">Occurs when arises specified event type for Tasks folder.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ServerNotificationEventArgs.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ServerNotificationEventArgs.html" target="_top">Frames</a></li>
<li><a href="ServerNotificationEventArgs.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
