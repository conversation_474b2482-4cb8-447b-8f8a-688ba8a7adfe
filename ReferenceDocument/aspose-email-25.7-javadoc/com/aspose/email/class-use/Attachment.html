<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.Attachment (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.Attachment (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Attachment.html" target="_top">Frames</a></li>
<li><a href="Attachment.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.Attachment" class="title">Uses of Class<br>com.aspose.email.Attachment</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ReferenceAttachment.html" title="class in com.aspose.email">ReferenceAttachment</a></strong></code>
<div class="block">
 This class represents a reference attachment</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">Attachment.</span><code><strong><a href="../../../../com/aspose/email/Attachment.html#createAttachmentFromString(java.lang.String,%20com.aspose.email.ContentType)">createAttachmentFromString</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;content,
                          <a href="../../../../com/aspose/email/ContentType.html" title="class in com.aspose.email">ContentType</a>&nbsp;contentType)</code>
<div class="block">
 Creates the attachment from string.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">Attachment.</span><code><strong><a href="../../../../com/aspose/email/Attachment.html#createAttachmentFromString(java.lang.String,%20java.lang.String)">createAttachmentFromString</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;content,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Creates the attachment from string.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">Attachment.</span><code><strong><a href="../../../../com/aspose/email/Attachment.html#createAttachmentFromString(java.lang.String,%20java.lang.String,%20java.nio.charset.Charset,%20java.lang.String)">createAttachmentFromString</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;content,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/nio/charset/Charset.html?is-external=true" title="class or interface in java.nio.charset">Charset</a>&nbsp;contentEncoding,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mediaType)</code>
<div class="block">
 Creates the attachment from string.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endFetchAttachment(com.aspose.ms.System.IAsyncResult)">endFetchAttachment</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous operation to complete.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchAttachment(com.aspose.email.IConnection,%20int,%20java.lang.String)">fetchAttachment</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
               int&nbsp;sequenceNumber,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName)</code>
<div class="block">
 Fetches the specified attachment</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchAttachment(int,%20java.lang.String)">fetchAttachment</a></strong>(int&nbsp;sequenceNumber,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentName)</code>
<div class="block">
 Fetches the specified attachment</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#fetchAttachment(java.lang.String)">fetchAttachment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentUri)</code>
<div class="block">
 Fetches the attachment</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchAttachment(java.lang.String)">fetchAttachment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentUri)</code>
<div class="block">
 Fetches the attachment</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericCollection&lt;<a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a>&gt;</code></td>
<td class="colLast"><span class="strong">IMessage.</span><code><strong><a href="../../../../com/aspose/email/IMessage.html#getAttachments()">getAttachments</a></strong>()</code>
<div class="block">
 Gets message attachments</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#addAttachment(com.aspose.email.Attachment)">addAttachment</a></strong>(<a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a>&nbsp;attachment)</code>
<div class="block">
 Add an attachment to message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">AttachmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AttachmentCollection.html#insertItem(int,%20com.aspose.email.Attachment)">insertItem</a></strong>(int&nbsp;index,
          <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a>&nbsp;item)</code>
<div class="block">
 Inserts an element into the <code>System.Collections.ObjectModel.Collection`1</code> at the specified index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="strong">AttachmentCollection.</span><code><strong><a href="../../../../com/aspose/email/AttachmentCollection.html#setItem(int,%20com.aspose.email.Attachment)">setItem</a></strong>(int&nbsp;index,
       <a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Attachment</a>&nbsp;item)</code>
<div class="block">
 Replaces the element at the specified index.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Attachment.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Attachment.html" target="_top">Frames</a></li>
<li><a href="Attachment.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
