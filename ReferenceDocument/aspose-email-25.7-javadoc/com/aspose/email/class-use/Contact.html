<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.Contact (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.Contact (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Contact.html" target="_top">Frames</a></li>
<li><a href="Contact.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.Contact" class="title">Uses of Class<br>com.aspose.email.Contact</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#findPeople(java.lang.String,%20int)">findPeople</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;queryString,
          int&nbsp;maxNumberOfItems)</code>
<div class="block">
 Find contacts located in the global address list (GAL) on server.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#findPeople(java.lang.String,%20com.aspose.email.MailQuery,%20int)">findPeople</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
          <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
          int&nbsp;maxNumberOfItems)</code>
<div class="block">
 Find contacts located in the specified user's personal mailbox on server.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#getAllContacts()">getAllContacts</a></strong>()</code>
<div class="block">
 Fetches all contacts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#getContact(com.aspose.email.Contact)">getContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContact(com.aspose.email.ObjectIdentifier)">getContact</a></strong>(<a href="../../../../com/aspose/email/ObjectIdentifier.html" title="class in com.aspose.email">ObjectIdentifier</a>&nbsp;contactId)</code>
<div class="block">
 Retrieves contact information according to specified identifier.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContact(com.aspose.email.ObjectIdentifier,%20int)">getContact</a></strong>(<a href="../../../../com/aspose/email/ObjectIdentifier.html" title="class in com.aspose.email">ObjectIdentifier</a>&nbsp;contactId,
          int&nbsp;options)</code>
<div class="block">
 Retrieves contact information according to specified identifier.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContact(java.lang.String)">getContact</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contactId)</code>
<div class="block">
 Retrieves contact information according to specified identifier.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#getContact(java.lang.String)">getContact</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contactUri)</code>
<div class="block">
 Fetches contact</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContact(java.lang.String,%20int)">getContact</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contactId,
          int&nbsp;options)</code>
<div class="block">
 Retrieves contact information according to specified identifier.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#getContacts(java.lang.String)">getContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Lists contacts located in the specified folder on server</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContacts(java.lang.String)">getContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder)</code>
<div class="block">
 Lists contacts located in the specified folder on server</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getContacts(java.lang.String,%20int)">getContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
           int&nbsp;options)</code>
<div class="block">
 Lists contacts located in the specified folder on server</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#getContactsFromGroup(java.lang.String)">getContactsFromGroup</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupId)</code>
<div class="block">
 Fetches contacts belonging to the group specified.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#getMailboxes()">getMailboxes</a></strong>()</code>
<div class="block">
 Lists mailboxes in the global address list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getMailboxes()">getMailboxes</a></strong>()</code>
<div class="block">
 Lists mailboxes having smtp addresses.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#load(java.io.InputStream)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is deprecated and will be removed soon, use the VCardContact and MapiContact classes to load from file or stream.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#load(java.io.InputStream,%20int)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
    int&nbsp;format)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is deprecated and will be removed soon, use the VCardContact and MapiContact classes to load from file or stream.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#load(java.lang.String)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filePath)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is deprecated and will be removed soon, use the VCardContact and MapiContact classes to load from file or stream.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#load(java.lang.String,%20int)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filePath,
    int&nbsp;format)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is deprecated and will be removed soon, use the VCardContact and MapiContact classes to load from file or stream.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#resolveContacts(java.lang.String)">resolveContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;unresolvedEntry)</code>
<div class="block">
 Resolves ambiguous mailbox display names.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#resolveContacts(java.lang.String)">resolveContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;unresolvedEntry)</code>
<div class="block">
 Resolves ambiguous mailbox display names.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#resolveContacts(java.lang.String,%20int)">resolveContacts</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;unresolvedEntry,
               int&nbsp;options)</code>
<div class="block">
 Resolves ambiguous e-mail addresses and display names
 Note: the maximum count of returned contacts is 100.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#to_Contact(com.aspose.email.MapiContact)">to_Contact</a></strong>(<a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email">MapiContact</a>&nbsp;contact)</code>
<div class="block">
 Converts <a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email"><code>MapiContact</code></a> to <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email"><code>Contact</code></a> object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#updateContact(com.aspose.email.Contact)">updateContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Updates contact</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#createContact(com.aspose.email.Contact)">createContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Creates a contact item in the Exchange store.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createContact(com.aspose.email.Contact)">createContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Creates a contact item in the Exchange store.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#createContact(com.aspose.email.Contact)">createContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Creates contact for specified email</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#createContact(com.aspose.email.Contact,%20java.lang.String)">createContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;emailAddress)</code>
<div class="block">
 Creates contact for specified email</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createContact(java.lang.String,%20com.aspose.email.Contact)">createContact</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
             <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Creates a contact item in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ContactPhoto.html" title="class in com.aspose.email">ContactPhoto</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#createContactPhoto(com.aspose.email.Contact,%20byte[])">createContactPhoto</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact,
                  byte[]&nbsp;imageData)</code>
<div class="block">
 Creates contact photo</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#deleteContact(com.aspose.email.Contact)">deleteContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Deletes the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#getContact(com.aspose.email.Contact)">getContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email">MapiContact</a></code></td>
<td class="colLast"><span class="strong">Contact.</span><code><strong><a href="../../../../com/aspose/email/Contact.html#to_MapiContact(com.aspose.email.Contact)">to_MapiContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Converts <a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email"><code>Contact</code></a> to <a href="../../../../com/aspose/email/MapiContact.html" title="class in com.aspose.email"><code>MapiContact</code></a> object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#updateContact(com.aspose.email.Contact)">updateContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Updates a contact item in the Exchange store.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a></code></td>
<td class="colLast"><span class="strong">IGmailClient.</span><code><strong><a href="../../../../com/aspose/email/IGmailClient.html#updateContact(com.aspose.email.Contact)">updateContact</a></strong>(<a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>&nbsp;contact)</code>
<div class="block">
 Updates contact</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/Contact.html" target="_top">Frames</a></li>
<li><a href="Contact.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
